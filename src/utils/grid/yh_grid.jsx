"use client";
import React, {
  useState,
  useEffect,
  useMemo,
  useCallback,
  useRef,
} from "react";
import {
  SearchOutlined,
  DownOutlined,
  EyeOutlined,
  EditOutlined,
  EyeInvisibleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons";
import {
  Button,
  Flex,
  notification,
  Space,
  Input,
  Dropdown,
  Tooltip,
  Modal,
  Select,
  InputNumber,
  Typography,
  Form,
  Checkbox,
  App,
  Empty,
  Alert,
  Image,
} from "antd";
import dynamic from "next/dynamic";
import { debounce, unionBy, merge } from "lodash";
import { AgGridReact } from "ag-grid-react";
import {
  AllEnterpriseModule,
  LicenseManager,
  ModuleRegistry,
} from "ag-grid-enterprise";
import { AgChartsCommunityModule } from "ag-charts-community";
import { useQueryClient } from "@tanstack/react-query";
import { handleStreamData } from "../stream_fetch";
import Api from "../api";
import "./yh_grid.css";
import { useBoundStore } from "../../store/store";
import { GridToolbarFields } from "../../../app/(main)/(full-width-content)/(pages)/template/template_content/component_options/form_fields/grid_toolbar_fields";
import LabelSelectionForm from "../forms/label_selection_form";
import CreateNewLabelForm from "../forms/create_new_label_form";
import AddNewColumnForm from "../forms/add_new_column_form";
import SaveTableSettingsForm from "../forms/save_table_settings_form";
import LoadTableSettingsForm from "../forms/load_table_settings_form";
import AddParametricColumnForm from "../forms/add_parametric_column_form";
import AddBinColumnForm from "../forms/add_bin_column_form";
import Helper from "../helper";
import { GridMenuFields } from "../../../app/(main)/(full-width-content)/(pages)/template/template_content/component_options/form_fields/grid_menu_fields";
import EngineeringTableFiltersForm from "../forms/engineering_table_filters_form";
import DuplicateRecipeForm from "../forms/duplicate_recipe_form";
import AddNewUserForm from "../forms/add_new_user_form";
import AddNewTeamForm from "../forms/add_new_team_form";
import { UserSettingsKeys } from "../user_settings_keys";
import TestsFilterForm from "../forms/tests_filter_form";
import TestTableFilterFormModal from "../../../app/(main)/(full-width-content)/(pages)/calculated_tests/step2/test_table_filter_form_modal";
import AddTestsFormModal from "../../../app/(main)/(full-width-content)/(pages)/calculated_tests/step2/add_tests_form_modal";
import ActionCellRenderer from "./grid_cell_renderers/actionCellRenderer";
import CustomHeader from "./customHeader";
import CustomCellRenderer from "./grid_cell_renderers/customCellRenderer";
import StringCellRenderer from "./grid_cell_renderers/stringCellRenderer";
import UsernameCellRenderer from "./grid_cell_renderers/usernameCellRenderer";
import StatusCellRenderer from "./grid_cell_renderers/statusCellRenderer";
import LabelCellRenderer from "./grid_cell_renderers/labelCellRenderer";
import VariableNameCellRenderer from "./grid_cell_renderers/variableNameCellRenderer";
import EditableCellRenderer from "./grid_cell_renderers/editableCellRenderer";
import GridHelper from "./grid_helper";
import TagCellRenderer from "./grid_cell_renderers/tagCellRenderer";
import { ComponentNameMapper } from "./component_name_mapper";
import SiteCellRenderer from "./grid_cell_renderers/siteCellRenderer";
import ColorPickerCellRenderer from "./grid_cell_renderers/colorPickerCellRenderer";
import NoRowsOverlayComponent from "./components/no_rows_overlay_component";
import RecipeAutoTriggerStatusFilter from "./components/recipe_auto_trigger_status_filter";
import ClosableTagCellRenderer from "./grid_cell_renderers/closableTagCellRenderer";
import LinkCellRenderer from "./grid_cell_renderers/linkCellRenderer";
import { diceAnalysisUpdateBoxplot } from "./hooks/on_pagination/dice_analysis_update_boxplot";
const ChartCellRenderer = dynamic(
  () => import("./grid_cell_renderers/chartCellRenderer"),
  {
    ssr: false,
  },
);

const { Text, Title, Paragraph } = Typography;

ModuleRegistry.registerModules([
  AllEnterpriseModule.with(AgChartsCommunityModule),
]);

// Set AG Grid enterprise license key
LicenseManager.setLicenseKey(process.env.NEXT_PUBLIC_AGGRID_LICENSE_KEY);

/**
 * Selection name mapper to be displayed in current selection info
 */
const selectionNameMapper = {
  lot_id: "Lot",
  data_struc_key: "Datalog",
  program_key_tnum_atnum: "Test",
};

/**
 * Invalid column definitions to be removed when setting grid columns
 */
const invalidColumnDefs = ["filterProps"];

/**
 * Column definitions that might change and should not be saved
 */
const dynamicColumnDefs = [
  "sort",
  "sortIndex",
  "filter",
  "type",
  "headerName",
  "aggFunc",
  "cellRenderer",
  "cellRendererParams",
  "headerComponent",
  "headerComponentParams",
  "wrapHeaderText",
];

/**
 * Regular expressions of columns that are dynamic and might change depending on available data
 */
const dynamicColumnRegexes = [/^site_[\d]+.*$/];

/**
 * Row ID segment separator and it might be used for getting row selection
 */
const rowIdSeparator = "|";

/**
 * Allowed statuses for deleting datalogs
 */
const datalogDeletionAllowedStatuses = ["Processed", "Error"];

/**
 * Remove invalid column definitions to avoid AG Grid warning
 *
 * @param {array} columns
 * @returns {array} updatedColumns
 */
const removeInvalidColumnDefs = (columns) => {
  const updatedColumns = columns.map((column) => {
    invalidColumnDefs.forEach((invalidColumnDef) => {
      delete column[invalidColumnDef];
    });
    return column;
  });

  return updatedColumns;
};

/**
 * Specify if row has per pin breakdown
 *
 * @param {object} dataItem
 * @param {object} gridFilters
 * @returns {boolean}
 */
const hasPerPinBreakdown = (dataItem, gridFilters) => {
  return dataItem.test_type === "m" && gridFilters.per_pin !== true;
};

/**
 * Specify if row has duplicates breakdown
 *
 * @param {object} dataItem
 * @returns {boolean}
 */
const hasDuplicatesBreakdown = (dataItem) => {
  return dataItem.duplicates > 0;
};

const gridCallbackActions = {
  hasPerPinBreakdown,
  hasDuplicatesBreakdown,
};

const filterComponents = {
  RecipeAutoTriggerStatusFilter,
};

/**
 * Grid common component
 *
 * @param {React.Ref} gridRef
 * @param {string} gridId
 * @param {object} gridOptions
 * @param {Form} gridFilterForm
 * @param {array} gridFilterFields
 * @param {object} component
 * @param {string} pageKey
 * @param {object} filters
 * @param {object} initialGridFilters
 * @param {object} cellActions
 * @param {array} rowGroups
 * @param {string} wrapperClassName
 * @param {object} eventHandlers
 * @param {object} cellRenderers
 * @param {object} valueSetters
 * @param {object} cellRendererSelectors
 * @param {object} conversionOptions
 * @param {function} onRowDataUpdated
 * @param {object} prerenderData
 * @param {React.Ref} ref
 * @returns {JSX.Element}
 */
export default React.forwardRef(function YHGrid(
  {
    gridRef,
    gridId,
    gridOptions = {},
    gridFilterForm,
    gridFilterFields = [],
    component,
    pageKey,
    filters,
    initialGridFilters = {},
    cellActions,
    rowGroups,
    wrapperClassName = "",
    eventHandlers = {},
    cellRenderers = {},
    valueSetters = {},
    cellRendererSelectors = {},
    conversionOptions = {},
    onRowDataUpdated,
    prerenderData = {},
  },
  ref,
) {
  const params = component.props.params;
  const settings = component.props.settings;

  const [isVisible, setIsVisible] = useState(
    settings.is_visible !== undefined ? settings.is_visible : true,
  );
  const [isGridReady, setIsGridReady] = useState(false);
  const [hasLoaded, setHasLoaded] = useState(false);
  const [gridHeight, setGridHeight] = useState(
    !isNaN(settings.height) ? `${settings.height}px` : settings.height,
  );
  const [gridTitle, setGridTitle] = useState(settings.title);
  const [gridFilters, setGridFilters] = useState(initialGridFilters);
  const [rowData, setRowData] = useState();
  const [curData, setCurData] = useState([]);
  const [pagination, setPagination] = useState(
    settings.pagination !== undefined ? settings.pagination : true,
  );
  const [pageSizeOptions, setPageSizeOptions] = useState([]);
  const [pageSize, setPageSize] = useState(100);
  const [currentSelectedRowIndex, setCurrentSelectedRowIndex] = useState();
  const [previousSelectedRowIndex, setPreviousSelectedRowIndex] = useState();
  const [loadingStatus, setLoadingStatus] = useState({});
  const [toolbarProps, setToolbarProps] = useState({});
  const [menuProps, setMenuProps] = useState({});
  const [isValidateSelection, setIsValidateSelection] = useState();
  const [isShowInvalid, setIsShowInvalid] = useState(false);
  const [shouldResetColumns, setShouldResetColumns] = useState(false);
  const [shouldGetGridData, setShouldGetGridData] = useState(false);
  const [gridRequestParams, setGridRequestParams] = useState({});
  const [quickFilter, setQuickFilter] = useState();
  const [searched, setSearched] = useState();
  const [
    isDisableSaveTableSettingsButton,
    setIsDisableSaveTableSettingsButton,
  ] = useState(true);
  const [
    isDisableDeleteTableSettingsButton,
    setIsDisableDeleteTableSettingsButton,
  ] = useState(true);
  const [
    isDisableLoadTableSettingsButton,
    setIsDisableLoadTableSettingsButton,
  ] = useState(true);
  const [shouldUpdateSavedTableOptions, setShouldUpdateSavedTableOptions] =
    useState(false);
  const [isAddLabelModalOpen, setIsAddLabelModalOpen] = useState(false);
  const [isFilterByLabelModalOpen, setIsFilterByLabelModalOpen] =
    useState(false);
  const [isCreateNewLabelModalOpen, setIsCreateNewLabelModalOpen] =
    useState(false);
  const [isAddNewColumnModalOpen, setIsAddNewColumnModalOpen] = useState(false);
  const [isSaveTableSettingsModalOpen, setIsSaveTableSettingsModalOpen] =
    useState(false);
  const [isLoadTableSettingsModalOpen, setIsLoadTableSettingsModalOpen] =
    useState(false);
  const [isAddParametricColumnModalOpen, setIsAddParametricColumnModalOpen] =
    useState(false);
  const [isAddBinColumnModalOpen, setIsAddBinColumnModalOpen] = useState(false);
  const [isFilterTableModalOpen, setIsFilterTableModalOpen] = useState(false);
  const [isAddNewTeamModalOpen, setIsAddNewTeamModalOpen] = useState(false);
  const [isAddNewTeamFormValuesValid, setIsAddNewTeamFormValuesValid] =
    useState(false);
  const [isTestsFilterModalOpen, setIsTestsFilterModalOpen] = useState(false);
  const [isTestTableFilterModalOpen, setIsTestTableFilterModalOpen] =
    useState(false);
  const [isAddTestsModalOpen, setIsAddTestsModalOpen] = useState(false);
  const [isDuplicateRecipeModalOpen, setIsDuplicateRecipeModalOpen] =
    useState(false);
  const [isAddNewUserModalOpen, setIsAddNewUserModalOpen] = useState(false);
  const [selectedRecipeData, setSelectedRecipeData] = useState({});
  const [isDisableAddUserButton, setIsDisableAddUserButton] = useState(false);
  const [hasUserSettings, setHasUserSettings] = useState(false);
  const [autoSizeColumnDefs, setAutoSizeColumnDefs] = useState([]);
  const [isEditMode, setIsEditMode] = useState(false);
  const [editedData, setEditedData] = useState({});
  const [updateEditedCellOriginalValue, setUpdateEditedCellOriginalValue] =
    useState(false);
  const [isLoadingData, setIsLoadingData] = useState(false);
  const [isLoadingDataDone, setIsLoadingDataDone] = useState(false);
  const [loadingData, setLoadingData] = useState({});
  const gridSelectionData = useBoundStore((state) => state.gridSelectionData);
  const setGridSelectionData = useBoundStore(
    (state) => state.setGridSelectionData,
  );
  const setSelectedPartIdData = useBoundStore(
    (state) => state.setSelectedPartIdData,
  );
  const setPendingLocalChartData = useBoundStore(
    (state) => state.setPendingLocalChartData,
  );
  const setSelectedRowsForNpiSimulation = useBoundStore(
    (state) => state.setSelectedRowsForNpiSimulation,
  );
  const currentPageData = useBoundStore((state) => state.currentPageData);
  const reloadGrids = useBoundStore((state) => state.reloadGrids);
  const reloadGrid = useBoundStore((state) => state.reloadGrid);
  const reloadGridFilters = useBoundStore((state) => state.reloadGridFilters);
  const setReloadGrids = useBoundStore((state) => state.setReloadGrids);
  const setReloadGrid = useBoundStore((state) => state.setReloadGrid);
  const setReloadGridFilters = useBoundStore(
    (state) => state.setReloadGridFilters,
  );
  const chartComponentRefs = useBoundStore((state) => state.chartComponentRefs);
  const reloadChartFilters = useBoundStore((state) => state.reloadChartFilters);
  const selectedStatusOption = useBoundStore(
    (state) => state.selectedStatusOption,
  );
  const setSelectedStatusOption = useBoundStore(
    (state) => state.setSelectedStatusOption,
  );
  const setSelectedDatalogProcessingSubconOption = useBoundStore(
    (state) => state.setSelectedDatalogProcessingSubconOption,
  );
  const tabsData = useBoundStore((state) => state.tabsData);
  const setRawDataTestNumber = useBoundStore(
    (state) => state.setRawDataTestNumber,
  );
  const presetAnalysisTemplates = useBoundStore(
    (state) => state.presetAnalysisTemplates,
  );
  const setConsolidationRecipeParams = useBoundStore(
    (state) => state.setConsolidationRecipeParams,
  );
  const urlParams = useBoundStore((state) => state.urlParams);
  const chartKeys = useBoundStore((state) => state.chartKeys);
  const mainContentRef = useBoundStore((state) => state.mainContentRef);
  const userData = useBoundStore((state) => state.userData);
  const processYhGridSocketEventQueue = useBoundStore(
    (state) => state.processYhGridSocketEventQueue,
  );
  const renderedComponents = useBoundStore((state) => state.renderedComponents);
  const yhGridSocketEventQueue = useBoundStore(
    (state) => state.yhGridSocketEventQueue,
  );
  const setRenderedComponents = useBoundStore(
    (state) => state.setRenderedComponents,
  );
  const detailGridComponents = useBoundStore(
    (state) => state.detailGridComponents,
  );
  const setNpiCharSimulationTestListGridRef = useBoundStore(
    (state) => state.setNpiCharSimulationTestListGridRef,
  );
  const gridComponentRefs = useBoundStore((state) => state.gridComponentRefs);
  const npiSimulationTestTabsRef = useBoundStore(
    (state) => state.npiSimulationTestTabsRef,
  );

  // Prevent re-triggering chart fetch/overlay for the same page slice (e.g., scrolling)
  const chartPageFetchInFlightRef = useRef(false);
  const lastChartSliceRef = useRef({ startRow: -1, endRow: -1 });
  const lastChartUpdateAtRef = useRef(0);

  const [{ confirm, error, warning }, contextHolder] = Modal.useModal();
  const [saveTableSettingsForm] = Form.useForm();
  const [loadTableSettingsForm] = Form.useForm();
  const [duplicateRecipeForm] = Form.useForm();
  const [addNewUserForm] = Form.useForm();
  const [filterTableForm] = Form.useForm();
  const [addNewTeamForm] = Form.useForm();
  const [testsFilterForm] = Form.useForm();
  const [testTableFilterForm] = Form.useForm();
  const [addTestsForm] = Form.useForm();
  const { message } = App.useApp();
  const queryClient = useQueryClient();
  const selectionStoreKey = component.selection_store_key
    ? component.selection_store_key
    : "";
  let detailGridSelectionStoreKey;
  const detailGridComponentsKey = `${pageKey}_${component.name}`;
  const stream = false;
  const userColumnDefs = useRef(settings.column_defs);
  const userDetailColumnDefs = useRef([]);
  const filteredColumnDefs = removeInvalidColumnDefs(
    Helper.cloneObject(settings.column_defs),
  );
  const [columnDefs, setColumnDefs] = useState(filteredColumnDefs);
  const sideBar = useMemo(() => {
    return (
      settings.side_bar ?? {
        toolPanels: [
          {
            id: "columns",
            labelDefault: "Columns",
            labelKey: "columns",
            iconKey: "columns",
            toolPanel: "agColumnsToolPanel",
            toolPanelParams: {
              suppressRowGroups: true,
              suppressValues: true,
              suppressPivotMode: true,
            },
          },
        ],
      }
    );
  }, []);
  const suppressRowClickSelection = true;
  const suppressRowVirtualisation =
    gridOptions.suppressRowVirtualisation !== undefined
      ? gridOptions.suppressRowVirtualisation
      : settings.suppress_row_virtualisation
        ? settings.suppress_row_virtualisation
        : false;
  const rowModelType =
    gridOptions.rowModelType !== undefined
      ? gridOptions.rowModelType
      : settings.is_server_side === true
        ? "serverSide"
        : "clientSide";
  const suppressClickEdit =
    gridOptions.suppressClickEdit !== undefined
      ? gridOptions.suppressClickEdit
      : false;
  const stopEditingWhenCellsLoseFocus =
    gridOptions.stopEditingWhenCellsLoseFocus !== undefined
      ? gridOptions.stopEditingWhenCellsLoseFocus
      : false;

  const masterDetail =
    gridOptions.masterDetail !== undefined
      ? gridOptions.masterDetail
      : settings.master_detail === true;

  const isRowMaster =
    gridOptions.isRowMaster !== undefined
      ? gridOptions.isRowMaster
      : typeof settings.is_row_master === "boolean"
        ? () => {
            return true;
          }
        : typeof settings.is_row_master === "string"
          ? (dataItem) =>
              gridCallbackActions[settings.is_row_master](dataItem, gridFilters)
          : () => {
              return false;
            };

  const gridWrapperRef = useRef();
  const gridPagingPanel = useRef();

  const components = useMemo(() => {
    return {
      CustomHeader: CustomHeader,
      EditOutlined: EditOutlined,
    };
  }, []);

  const defaultColDef = useMemo(() => {
    return {
      sortable: true,
      resizable: true,
      wrapHeaderText: true,
      autoHeaderHeight: true,
      enableCellChangeFlash: true,
      sortingOrder: ["asc", "desc"],
      filter: "agTextColumnFilter",
    };
  }, []);

  /**
   * Rules which can be applied to include certain CSS classes
   */
  const rowClassRules = useMemo(() => {
    return {
      "row-invalid": function (params) {
        // do not use strict comparison since value will become either boolean or string
        return params.data && params.data.tagged_invalid == 1;
      },
    };
  }, []);

  /**
   * Update the grid cell with the new data and assign it to editedData state
   *
   * @param {object} node - node to update
   * @param {string} colId - column id
   * @param {string} newValue - new value to set
   */
  const updateEditedData = (node, colId, newValue) => {
    node.updateData({
      ...node.data,
      [colId]: newValue,
    });

    editedData[node.data.data_struc_key] = {
      ...editedData[node.data.data_struc_key],
      [colId]: newValue,
    };
  };

  /**
   * Reload grid data
   *
   * @returns {AbortController}
   */
  const reloadData = () => {
    let abortCtl = Api.initAbortCtl();

    if (isServerSide()) {
      if (gridRef.current.api) {
        gridRef.current.api.refreshServerSide({ purge: true });
      }
    } else {
      setRowData(undefined);
      if (stream === "1") {
        handleStreamData(setCurData, params);
      } else {
        abortCtl = getGridData(params, gridRequestParams);
      }
    }

    return abortCtl;
  };

  /**
   * Get column types
   *
   * @param {object} gridComponent
   * @param {array} componentColumnDefs
   * @returns {object} columnTypes
   */
  const getColumnTypes = useCallback(
    (gridComponent, componentColumnDefs) => {
      let columnTypes = {};
      if (gridComponent) {
        columnTypes = {
          groupColumn: {
            showRowGroup: true,
            cellRenderer: "agGroupCellRenderer",
            cellRendererParams: {
              // turn off the row count
              suppressCount: true,
              // turn off double click for expand
              suppressDoubleClickExpand: true,
              // provide an inner renderer
              innerRenderer: StringCellRenderer,
            },
          },
          stringColumn: {
            cellRenderer: StringCellRenderer,
            cellRendererParams: {
              componentColumnDefs,
            },
          },
          testTypeColumn: {
            valueFormatter: GridHelper.testTypeFormatter,
          },
          passFailColumn: {
            cellRenderer: (props) => {
              let value = props.value;
              if (value === "Pass") {
                value = <Text style={{ color: "#52C41A" }}>Pass</Text>;
              } else if (value === "Fail") {
                value = <Text style={{ color: "#FF4D4F" }}>Fail</Text>;
              }
              return value;
            },
          },
          // Should use natural sorting
          stringAndNumberColumn: {
            comparator: (a, b) => {
              const valueA = a || "";
              const valueB = b || "";
              return valueA.localeCompare(valueB, undefined, {
                numeric: true,
                sensitivity: "base",
              });
            },
          },
          numberColumn: {
            valueFormatter: GridHelper.numberFormatter,
            cellClass: "ag-right-aligned-cell",
            comparator: GridHelper.numberComparator,
          },
          // Number without separator, .e.g Test number columns
          numberWithoutSeparatorFormatterColumn: {
            cellClass: "ag-right-aligned-cell",
            comparator: GridHelper.numberComparator,
          },
          percentageColumn: {
            valueFormatter: GridHelper.percentageFormatter,
            cellClass: "ag-right-aligned-cell",
          },
          dateColumn: {
            valueFormatter: GridHelper.dateFormatter,
          },
          dateStringColumn: {
            cellRenderer: StringCellRenderer,
            comparator: GridHelper.dateStringComparator,
          },
          labelColumn: {
            cellRenderer: LabelCellRenderer,
          },
          clickableColumn: {
            cellRenderer: cellRenderers?.clickableColumn ?? LinkCellRenderer,
            cellRendererParams: {
              prerenderData: prerenderData,
            },
          },
          toTagsColumn: {
            cellRenderer: TagCellRenderer,
          },
          editableTagsColumn: {
            cellRenderer: cellRenderers?.editableTagsColumn,
          },
          closableTagsColumn: {
            cellRenderer: ClosableTagCellRenderer,
            cellRendererParams: {
              filters: Helper.getPageFilters(filters, pageKey),
            },
          },
          customActionColumn: {
            cellRenderer: cellRenderers?.customActionColumn,
          },
          booleanYesNoColumn: {
            valueFormatter: GridHelper.booleanYesNoFormatter,
          },
          checkboxColumn: {
            resizable: false,
            sortable: false,
            filter: false,
            lockVisible: true,
            lockPosition: true,
            suppressMovable: true,
            suppressSizeToFit: true,
            suppressAutoSize: true,
            suppressColumnsToolPanel: true,
            pinned: "left",
            width: 50,
          },
          iconColumn: {
            resizable: false,
            sortable: false,
            suppressSizeToFit: true,
            suppressAutoSize: true,
            suppressColumnsToolPanel: true,
            width: 70,
            cellRenderer: CustomCellRenderer,
            cellRendererParams: {
              cellActions,
              componentColumnDefs,
              pageKey,
              filters: Helper.getPageFilters(filters, pageKey),
              queryClient,
            },
          },
          actionColumn: {
            resizable: true,
            suppressSizeToFit: true,
            suppressAutoSize: true,
            initialWidth: 100,
            cellRenderer: ActionCellRenderer,
            cellRendererParams: {
              cellActions,
              componentColumnDefs,
              filters: Helper.getPageFilters(filters, pageKey),
              pageKey,
            },
          },
          numberSystemColumn: {
            cellRenderer: (params) => conversionOptions[params.value] ?? "",
            cellEditorParams: {
              values: Object.keys(conversionOptions),
              formatValue: (value) => conversionOptions[value] ?? "",
            },
            onCellValueChanged: (event) => {
              // Update some row values based on the selected conversion
              const newCellValues = {
                min_val: event.data[`min_val_${event.newValue}`],
                max_val: event.data[`max_val_${event.newValue}`],
                max_chars: event.data[`max_chars_${event.newValue}`],
              };
              const newData = { ...event.data, ...newCellValues };
              event.node.setData(newData);
            },
          },
          variableNameColumn: {
            valueSetter: valueSetters.variableNameColumn,
            cellRenderer: VariableNameCellRenderer,
          },
          maxCharColumn: {
            valueSetter: valueSetters.maxCharColumn,
            cellRenderer: EditableCellRenderer,
            cellRendererParams: {
              componentColumnDefs,
            },
          },
          editableColumn: {
            editable: (params) => {
              return params.data.editable !== false;
            },
            valueSetter: valueSetters.editableColumn,
            cellRenderer: EditableCellRenderer,
            cellRendererParams: {
              componentColumnDefs,
              isEditMode,
              gridRef,
              updateEditedData,
              updateEditedCellOriginalValue,
              setUpdateEditedCellOriginalValue,
            },
          },
          editedOutputYHFieldColumn: {
            cellRendererSelector:
              cellRendererSelectors.editedOutputYHFieldColumn,
          },
          customColumn: {
            initialWidth: 100,
            cellRenderer: CustomCellRenderer,
            cellRendererParams: {
              cellActions,
              componentColumnDefs,
              pageKey,
              filters: Helper.getPageFilters(filters, pageKey),
              queryClient,
              gridId,
              reloadData,
            },
          },
          usernameColumn: {
            cellRenderer: UsernameCellRenderer,
          },
          statusColumn: {
            cellRenderer: StatusCellRenderer,
          },
          siteColumn: {
            cellRenderer: SiteCellRenderer,
          },
          fileSizeColumn: {
            valueFormatter: GridHelper.fileSizeFormatter,
            cellStyle: (params) => {
              const { new_size, existing_size } = params.data;
              const { colId } = params.column;
              if (colId === "new_size" && existing_size && existing_size) {
                return {
                  color: new_size < existing_size ? "red" : null,
                  fontWeight: new_size > existing_size ? "bold" : null,
                };
              }
            },
          },
          durationColumn: {
            valueFormatter: GridHelper.durationFormatter,
          },
          colorPickerColumn: {
            cellRenderer: ColorPickerCellRenderer,
            cellRendererParams: {
              gridRef,
            },
          },
          chartColumn: {
            cellRenderer: ChartCellRenderer,
            cellRendererParams: {
              pageKey,
              prerenderData,
              filters,
            },
          },
        };
      }

      return columnTypes;
    },
    [userColumnDefs.current, userDetailColumnDefs.current, isEditMode],
  );

  const columnTypes = useMemo(() => {
    return getColumnTypes(component, userColumnDefs.current);
  }, [component, userColumnDefs.current]);

  /**
   * Check if grid is using server side row model or not
   * @returns {boolean} isServerSide
   */
  const isServerSide = () => {
    const isServerSide = rowModelType === "serverSide";
    return isServerSide;
  };

  /**
   * Set rowID of ungroup columns
   *
   * @param {array} tableUniqueCols
   * @param {object} data
   * @returns {string} rowID
   */
  const getLeafRowID = (tableUniqueCols, data) => {
    let rowID = "leaf";
    tableUniqueCols.forEach((col) => {
      rowID += rowIdSeparator + data[col];
    });
    return rowID;
  };

  /**
   * Callback to provide row id selection
   * Used `|` as row id segment separator and it might be used for getting row selection
   *
   * @returns {string} rowID
   */
  const getRowId = useMemo(() => {
    let rowID;
    const tableUniqueCols = component.table_unique_cols;
    const tableUniqueGroupCols = component.table_unique_group_cols;
    return ({ api, data, level, parentKeys = [] }) => {
      const groupColumns = api.getRowGroupColumns();
      if (
        tableUniqueCols &&
        tableUniqueCols.length > 0 &&
        groupColumns.length === 0
      ) {
        rowID = getLeafRowID(tableUniqueCols, data);
      } else {
        if (groupColumns.length > level) {
          let rowIDKeys = [...parentKeys];
          const field = groupColumns[level].getColDef().field;
          rowIDKeys.push(data[field]);
          if (tableUniqueGroupCols) {
            tableUniqueGroupCols.forEach((col) => {
              rowIDKeys.push(data[col]);
            });
          }
          rowID = rowIDKeys.join(rowIdSeparator);
        } else {
          let rowIDKeys = [...parentKeys];
          if (tableUniqueCols) {
            tableUniqueCols.forEach((col) => {
              rowIDKeys.push(data[col]);
            });
          }
          rowID = rowIDKeys.join(rowIdSeparator);
        }
      }

      return rowID;
    };
  }, []);

  const additionalGridOptions = useMemo(() => {
    return isServerSide()
      ? {
          cacheBlockSize: pageSize,
          maxBlocksInCache: 5,
          getRowId: getRowId,
        }
      : {
          rowData: rowData,
          onRowDataUpdated:
            typeof onRowDataUpdated === "function"
              ? onRowDataUpdated
              : () => {
                  if (settings.on_row_data_updated) {
                    eventActions[settings.on_row_data_updated]();
                  }
                },
        };
  }, [rowData]);

  /**
   * Filter columns by manufacturing process
   * Filter columns by row grouping field
   * Hide unmatched columns and remove it from columns tool panel
   *
   * @param {array} componentColumnDefs
   * @param {array} columns
   * @returns {array} columns
   */
  const filterColumnsByFilterProps = (columns, componentColumnDefs) => {
    // Filter columns by mfg process
    const pageFilters = Helper.getPageFilters(filters, pageKey);
    const mfgProcesses = pageFilters.manufacturing_process
      ? pageFilters.manufacturing_process.split(",")
      : pageFilters.mfg_process
        ? pageFilters.mfg_process.split(",")
        : [];
    const mfgProcessColumnFields = GridHelper.getMfgProcessColumnFields(
      componentColumnDefs,
      mfgProcesses,
    );

    columns = columns.filter((column) => {
      return mfgProcessColumnFields.indexOf(column.field) !== -1;
    });

    // Filter columns by row grouping field
    if (Array.isArray(rowGroups)) {
      const invalidGroupFields = componentColumnDefs
        .filter((column) => {
          return (
            column.filterProps &&
            Array.isArray(column.filterProps.groupBy) &&
            !column.filterProps.groupBy.some((element) =>
              rowGroups.includes(element),
            )
          );
        })
        .map((column) => {
          return column.field;
        });
      columns = columns.filter((column) => {
        return invalidGroupFields.indexOf(column.field) === -1;
      });
    }

    return columns;
  };

  /**
   * Get columns that has filter value
   *
   * @param {array} componentColumnDefs
   */
  const getFilteredColumns = (componentColumnDefs) => {
    const pageFilters = Helper.getPageFilters(filters, pageKey);
    const filteredColumns = Object.keys(pageFilters).filter((filterKey) => {
      return (
        Helper.filterArrayAndFind(componentColumnDefs, "field", filterKey) !==
        null
      );
    });
    return filteredColumns;
  };

  /**
   * Generate column definitions
   *
   * @param {object} newColumnDefs
   * @param {array} componentColumnDefs
   * @returns {array} updatedColumnDefs - array of objects of column definition
   */
  const generateColumnDefinitions = (newColumnDefs, componentColumnDefs) => {
    let updatedColumnDefs = [];
    const filteredColumns = getFilteredColumns(newColumnDefs);
    newColumnDefs.forEach((newColumnDef) => {
      if (newColumnDef.field !== "checkbox") {
        let column = newColumnDef.field
          ? Helper.filterArrayAndFind(
              componentColumnDefs,
              "field",
              newColumnDef.field,
            )
          : undefined;
        if (column) {
          Object.keys(newColumnDef).forEach((key) => {
            if (newColumnDef[key] !== null || key === "pinned") {
              column[key] = newColumnDef[key];
            }
          });
          // to remove pinned column when resetting columns
          if (
            column.pinned === undefined &&
            column.initialPinned === undefined
          ) {
            column.pinned = null;
          }
        } else {
          column = newColumnDef;
        }

        if (column.field) {
          column.colId = column.field;
        }
        // Ensure dynamic columns have a readable header.
        if (!column.headerName || String(column.headerName).trim() === "") {
          const hAlt =
            column.header_name ??
            column.header ??
            column.label ??
            column.display_name;
          if (hAlt && String(hAlt).trim() !== "") {
            column.headerName = hAlt;
          } else if (typeof column.field === "string") {
            const f = column.field;
            column.headerName = Helper.titlelize(f);
          }
        }
        if (Array.isArray(rowGroups)) {
          column.rowGroup = rowGroups.indexOf(column.field) !== -1;
        }
        if (column.valueGetter !== undefined) {
          column.valueGetter = GridHelper[column.valueGetter];
        }
        if (column.valueFormatter !== undefined) {
          column.valueFormatter = GridHelper[column.valueFormatter];
        }
        if (column.comparator !== undefined) {
          column.comparator = GridHelper[column.comparator];
        }
        if (
          column.filterProps &&
          (column.filterProps.wrap === true || column.headerComponentParams)
        ) {
          column.headerComponent = CustomHeader;
          const sortModel =
            gridRequestParams &&
            gridRequestParams.request &&
            gridRequestParams.request.sortModel
              ? gridRequestParams.request.sortModel
              : [];
          column.headerComponentParams = column.headerComponentParams
            ? { ...column.headerComponentParams, ...{ sortModel: sortModel } }
            : { sortModel: sortModel };
        }
        if (column.filterProps && column.filterProps.filter) {
          column.filter = filterComponents[column.filterProps.filter];
        }
        if (filteredColumns.indexOf(column.field) !== -1) {
          column.hide = false;
        }
        updatedColumnDefs.push(column);
      }
    });
    updatedColumnDefs = removeInvalidColumnDefs(updatedColumnDefs);

    return updatedColumnDefs;
  };

  /**
   * Fired when grid selection will change
   * Set grid selection global state
   *
   * @param {SelectionChangedEvent} event
   */
  const onSelectionChanged = (event) => {
    const isServerSide =
      event.api.getGridOption("rowModelType") === "serverSide";
    let selectedNodes = GridHelper.getSelectedNodes(
      gridRef.current,
      isServerSide,
    );
    let selectedRows = isServerSide
      ? selectedNodes.map((node) => {
          return node.data;
        })
      : gridRef.current.api.getSelectedRows();

    const gridSelectionDataCopy = Helper.cloneObject(gridSelectionData);
    gridSelectionDataCopy[selectionStoreKey] = selectedRows;

    if (detailGridComponents[detailGridComponentsKey][component.name]) {
      getDetailGridSelectionData(
        gridSelectionDataCopy,
        detailGridComponents[detailGridComponentsKey][component.name],
        gridRef.current,
        selectedNodes,
        selectedRows,
      );
    }

    setGridSelectionData(gridSelectionDataCopy);
    updateSelectOptionsBtn(selectedNodes);

    if (typeof eventHandlers.onSelectionChanged === "function") {
      eventHandlers.onSelectionChanged(event);
    }
  };

  /**
   * Get selection data of detail grid
   *
   * @param {object} gridSelectionDataCopy
   * @param {object} detailGridComponent
   * @param {object} masterGridOptions
   * @param {array} selectedNodes
   * @param {array} selectedRows
   */
  const getDetailGridSelectionData = (
    gridSelectionDataCopy,
    detailGridComponent,
    masterGridOptions,
    selectedNodes,
    selectedRows,
  ) => {
    if (detailGridComponent) {
      let detailGridSelectedNodes = [];
      masterGridOptions.api.forEachDetailGridInfo((detailGrid) => {
        detailGridSelectedNodes = detailGridSelectedNodes.concat(
          GridHelper.getSelectedNodes(detailGrid, false),
        );

        selectedNodes.push(...detailGridSelectedNodes);
        removeDuplicateRowNodes(selectedNodes);

        gridSelectionDataCopy[detailGridComponent.selection_store_key] =
          detailGridSelectedNodes.map((node) => {
            return node.data;
          });

        if (
          detailGridComponents[detailGridComponentsKey][
            detailGridComponent.name
          ]
        ) {
          getDetailGridSelectionData(
            gridSelectionDataCopy,
            detailGridComponents[detailGridComponentsKey][
              detailGridComponent.name
            ],
            detailGrid,
            selectedNodes,
            selectedRows,
          );
        }
      });
    }
  };

  /**
   * Remove duplicate row nodes
   *
   * @param {array} nodes
   */
  const removeDuplicateRowNodes = (nodes) => {
    const seenIds = new Set();
    for (let i = nodes.length - 1; i >= 0; i--) {
      const id = nodes[i].id;
      if (seenIds.has(id)) {
        nodes.splice(i, 1);
      } else {
        seenIds.add(id);
      }
    }
  };

  /**
   * Fired when grid sorting will change
   */
  const onSortChanged = () => {
    if (settings.on_sort_changed) {
      eventActions[settings.on_sort_changed]();
    }
  };

  /**
   * Fired when grid filter model changes
   */
  const onFilterChanged = () => {
    if (settings.on_filter_changed) {
      eventActions[settings.on_filter_changed]();
    }
  };

  /**
   * Fired when a row is selected or deselected
   *
   * @param {RowSelectedEvent} event
   */
  const onRowSelected = (event) => {
    // store selection index for range selection
    if (event.node.isSelected()) {
      setPreviousSelectedRowIndex(currentSelectedRowIndex);
      setCurrentSelectedRowIndex(event.node.rowIndex);
    } else {
      setPreviousSelectedRowIndex();
    }
    if (typeof eventHandlers.onRowSelected === "function") {
      eventHandlers.onRowSelected(event);
    }
  };

  /*
   * Fired when cell editing stopped
   *
   * @param {CellEditingStoppedEvent} event
   * */
  const onCellEditingStopped = (event) => {
    if (settings.on_cell_editing_stopped) {
      eventActions[settings.on_cell_editing_stopped](event);
    }
    if (typeof eventHandlers.onCellEditingStopped === "function") {
      eventHandlers.onCellEditingStopped(event);
    }
  };

  /*
   * Fired when cell editing started
   *
   * @param {CellEditingStartedEvent} event
   * */
  const onCellEditingStarted = (event) => {
    if (typeof eventHandlers.onCellEditingStarted === "function") {
      eventHandlers.onCellEditingStarted(event);
    }
  };

  /**
   * Fired when cell is clicked
   * Disable row selection when clicking action items inside cell content
   *
   * @param {CellClickedEvent} event
   */
  const onCellClicked = (event) => {
    let selectRow = true;
    if (event.column.colDef?.cellRendererParams?.componentColumnDefs) {
      const suppressRowClickColumns =
        event.column.colDef.cellRendererParams.componentColumnDefs
          .filter((colDef) => {
            return (
              colDef.filterProps &&
              colDef.filterProps.suppressRowClickSelection === true
            );
          })
          .map((colDef) => {
            return colDef.field;
          });

      const columnSuppressed =
        suppressRowClickColumns.indexOf(event.column.colId) !== -1;
      const linkClicked = event.node.suppressRowSelection;

      if (columnSuppressed || linkClicked) {
        selectRow = false;
        delete event.node.suppressRowSelection;
      }
    } else if (
      event.column.colDef.cellEditorParams ||
      event.column.colDef.type === "closableTagsColumn"
    ) {
      // Suppress row click selection for columns with cell editor or closable tags
      selectRow = false;
    }

    const rowSelection = event.api.getGridOption("rowSelection");
    if (selectRow && rowSelection?.checkboxes === true) {
      event.node.setSelected(!event.node.isSelected());
    }
  };

  /**
   * Generate column definitions of detail grid
   *
   * @param {object} detailGridComponent
   * @param {array} detailColumnDefs
   * @returns {array} detailColumnDefs
   */
  const generateDetailColumnDefinitions = (
    detailGridComponent,
    detailColumnDefs = [],
  ) => {
    userDetailColumnDefs.current = Helper.cloneObject(detailColumnDefs);
    detailColumnDefs = removeInvalidColumnDefs(
      Helper.cloneObject(detailColumnDefs),
    );
    detailColumnDefs = filterColumnsByFilterProps(
      detailColumnDefs,
      userDetailColumnDefs.current,
    );
    detailColumnDefs = generateColumnDefinitions(
      detailColumnDefs,
      Helper.cloneObject(userDetailColumnDefs.current),
    );

    return detailColumnDefs;
  };

  /**
   * Detail cell renderer parameters used for rendering master row breakdown grid
   *
   * @param {object} detailGridComponent
   * @param {object} masterGridOptions
   * @returns {object} cellRendererParams
   */
  const detailCellRendererParams = (detailGridComponent) => {
    // initialize detail grid selection store
    detailGridSelectionStoreKey = detailGridComponent.selection_store_key;
    if (gridSelectionData[detailGridSelectionStoreKey] === undefined) {
      gridSelectionData[detailGridSelectionStoreKey] = [];
    }
    const detailColumnDefs = generateDetailColumnDefinitions(
      detailGridComponent,
      detailGridComponent?.props.settings.column_defs,
    );
    let cellRendererParams = {
      refreshStrategy: "nothing",
    };
    cellRendererParams.detailGridOptions = {
      getRowId: (rowParams) => {
        const tableUniqueCols = detailGridComponent.table_unique_cols;
        let rowIdArr = ["detail"];
        tableUniqueCols.forEach((col) => {
          rowIdArr.push(rowParams.data[col]);
        });
        const rowId = rowIdArr.join(rowIdSeparator);
        return rowId;
      },
      columnDefs: detailColumnDefs,
      defaultColDef: defaultColDef,
      columnTypes: getColumnTypes(
        detailGridComponent,
        userDetailColumnDefs.current,
      ),
      accentedSort: true,
      domLayout: "autoHeight",
      rowSelection: {
        mode:
          detailGridComponent?.props.settings.rowSelection === "single"
            ? "singleRow"
            : "multiRow",
        enableClickSelection: !suppressRowClickSelection,
        checkboxes:
          detailGridComponent?.props.settings.checkbox_selection ?? false,
        headerCheckbox:
          detailGridComponent?.props.settings.header_checkbox_selection ??
          false,
        enableSelectionWithoutKeys: true,
      },
      autoSizeStrategy: {
        type:
          detailGridComponent?.props.settings.auto_size_columns === false
            ? "fitProvidedWidth"
            : "fitCellContents",
      },
      suppressColumnVirtualisation: true,
      suppressRowVirtualisation: suppressRowVirtualisation,
      masterDetail: detailGridComponent?.props.settings.master_detail === true,
      isRowMaster: () =>
        detailGridComponent?.props.settings.is_row_master === true,
      detailRowAutoHeight: true,
      onSelectionChanged: onSelectionChanged,
      onCellClicked: onCellClicked,
    };
    cellRendererParams.getDetailRowData = (masterRowParams) => {
      getDetailGridData(detailGridComponent, masterRowParams);
    };

    return cellRendererParams;
  };

  useEffect(() => {
    // Process grid socket event if page is active, component is rendered on the current page and websockets component key exists in yhGridSocketEventQueue
    if (component?.websockets?.component_key) {
      const socketComponentKey = getGridSocketComponentKey();
      if (
        currentPageData.key === pageKey &&
        renderedComponents[pageKey]?.includes(component?.name) &&
        Object.keys(yhGridSocketEventQueue).includes(socketComponentKey)
      ) {
        processGridSocketEventQueue(socketComponentKey);
      }
    }
  }, [currentPageData.key, processYhGridSocketEventQueue]);

  useEffect(() => {
    const sizeOptions = getPageSizeOptions();
    setPageSizeOptions(sizeOptions);
    // initialize grid selection store
    if (gridSelectionData[selectionStoreKey] === undefined) {
      gridSelectionData[selectionStoreKey] = [];
    }
    if (detailGridComponents[detailGridComponentsKey] === undefined) {
      detailGridComponents[detailGridComponentsKey] = {};
    }
    if (
      component.name ===
        ComponentNameMapper.npi_char_simulation_test_list_table ||
      component.name ===
        ComponentNameMapper.npi_gage_simulation_test_list_table ||
      component.name ===
        ComponentNameMapper.npi_die_drift_simulation_test_list_table
    ) {
      setNpiCharSimulationTestListGridRef(gridRef);
    }
    gridComponentRefs[gridId] = ref;
  }, []);

  React.useImperativeHandle(ref, () => ({
    reloadGridData: () => {
      reloadData();
    },
    isGridReady: () => {
      return isGridReady;
    },
    getComponent: () => {
      return component;
    },
    getReloadGridKey: () => {
      return component.reload_grid_key;
    },
  }));

  useEffect(() => {
    let _rowData = rowData ? [...rowData] : [];
    if (curData.length) {
      _rowData.push(...curData);
      setRowData(_rowData);
    }
  }, [curData]);

  useEffect(() => {
    if (gridRef.current && gridRef.current.api) {
      gridRef.current.api.setGridOption("columnDefs", columnDefs);
    }
  }, [columnDefs]);

  useEffect(() => {
    updateShowInvalidBtn();
  }, [rowGroups]);

  useEffect(() => {
    if (shouldResetColumns) {
      resetColumns();
    }
  }, [shouldResetColumns]);

  useEffect(() => {
    if (gridRef.current && gridRef.current.api) {
      updateToolbarButtonState();
    }
  }, [
    gridSelectionData[selectionStoreKey],
    gridSelectionData[detailGridSelectionStoreKey],
    hasLoaded,
  ]);

  useEffect(() => {
    updateValidateSelectionBtn();
  }, [isValidateSelection]);

  useEffect(() => {
    if (
      component.props.toolbar &&
      component.props.toolbar.some((menu) => menu.key === "showInvalid")
    ) {
      applyShowInvalidFilter();
      updateShowInvalidBtn();
    }
  }, [isShowInvalid]);

  useEffect(() => {
    if (
      component.props.menu &&
      component.props.menu.some(
        (menu) => menu.key === "showWaferIdBreakdownTable",
      )
    ) {
      updateShowWaferIdBreakdownTableBtn();
      if (isVisible && !hasLoaded) {
        reloadData();
      }
    }
  }, [isVisible]);

  useEffect(() => {
    if (shouldGetGridData) {
      setShouldGetGridData(!shouldGetGridData);
      getGridData(params, gridRequestParams, quickFilter);
    }
  }, [shouldGetGridData]);

  useEffect(() => {
    if (reloadGrids.indexOf(component.reload_grid_key) !== -1) {
      if (
        component.name === ComponentNameMapper.lot_yield_trend_table &&
        reloadGridFilters[component.reload_grid_key].lot_count
      ) {
        generateYieldTrendData(
          reloadGridFilters[component.reload_grid_key].lot_count,
        );
      } else {
        getGridData(params, gridRequestParams, quickFilter);
      }
    }
  }, [reloadGrid]);

  useEffect(() => {
    setDownloadTableBtnState(isLoadingData ?? false);
  }, [isLoadingData]);

  /**
   * Generate yield trend data from existing rows or fetch data from server based on requested lot count
   *
   * @param {int} lotCount
   */
  const generateYieldTrendData = (lotCount) => {
    const rowsData = GridHelper.getAllRowsData(gridRef.current);
    if (lotCount <= rowsData.length) {
      const selectedLotId = filters[pageKey].lot_id;
      const lotIds = rowsData.map((row) => {
        return row.lot_id;
      });
      const selectedLotIndex = lotIds.indexOf(selectedLotId);
      const rows = Helper.getArrayElementsAroundIndex(
        rowsData,
        selectedLotIndex,
        lotCount,
      );
      setRowData(rows);
    } else {
      getGridData(params, gridRequestParams, quickFilter);
    }
  };

  /**
   * Handles the processing of yh grid websocket event
   *
   * @param {string} socketComponentKey
   */
  const processGridSocketEventQueue = useCallback((socketComponentKey) => {
    let tableUniqueCols = component.table_unique_cols;
    let isEventCompleted = true;
    yhGridSocketEventQueue[socketComponentKey]?.forEach(
      ({ action, payload }) => {
        payload = !Array.isArray(payload) ? [payload] : payload;
        payload?.forEach((data) => {
          let success = true;
          switch (action) {
            case "add_update":
              addUpdateTableRow(tableUniqueCols, data);
              break;
            case "update":
              success = updateTableRow(tableUniqueCols, data);
              break;
            case "delete":
              success = deleteTableRow(tableUniqueCols, data);
              break;
            case "update_loading":
              updateTableLoadingData(data);
              break;
          }
          if (!success) {
            isEventCompleted = false;
          }
        });
      },
    );

    // Hide/displays no row overlay after transactions
    gridRef.current.api.getDisplayedRowCount() > 0
      ? gridRef.current.api.hideOverlay()
      : gridRef.current.api.showNoRowsOverlay();

    if (isEventCompleted) {
      // Remove the grid socket event from the queue after processing
      delete yhGridSocketEventQueue[socketComponentKey];
    }
  }, []);

  /**
   * Handler on whether to update or add the node from websocket yh_event
   *
   * @param {array} tableUniqueCols
   * @param {object} data
   */
  const addUpdateTableRow = (tableUniqueCols, data) => {
    let rowNode = gridRef.current.api.getRowNode(
      getLeafRowID(tableUniqueCols, data),
    );

    if (rowNode) {
      rowNode.updateData({
        ...rowNode.data,
        ...data,
      });
    } else {
      gridRef.current.api.applyServerSideTransaction({
        add: [data],
        addIndex: 0,
      });
    }
  };

  /**
   * Handler to update the node from websocket yh_event
   *
   * @param {array} tableUniqueCols
   * @param {object} data
   * @returns {boolean} success
   */
  const updateTableRow = (tableUniqueCols, data) => {
    let success = false;
    let rowNode = gridRef.current.api.getRowNode(
      getLeafRowID(tableUniqueCols, data),
    );

    if (rowNode) {
      rowNode.updateData({
        ...rowNode.data,
        ...data,
      });
      success = true;
    }

    return success;
  };

  /**
   * Handler to delete the node from websocket yh_event
   *
   * @param {array} tableUniqueCols
   * @param {object} data
   * @returns {boolean} success
   */
  const deleteTableRow = (tableUniqueCols, data) => {
    let success = false;
    let rowNode = gridRef.current.api.getRowNode(
      getLeafRowID(tableUniqueCols, data),
    );

    if (rowNode) {
      gridRef.current.api.applyServerSideTransaction({
        remove: [data],
      });
      success = true;
    }

    return success;
  };

  /**
   * Handler to update the loading data from websocket yh_event
   *
   * @param {object} data
   */
  const updateTableLoadingData = (data) => {
    setLoadingData(data);
    setIsLoadingData(data.count < data.total);
    if (data.count === data.total) {
      setIsLoadingDataDone(true);
      setTimeout(() => {
        setIsLoadingDataDone(false);
      }, 5000);
    }
  };

  /**
   * Update the state of toolbar buttons
   */
  const updateToolbarButtonState = () => {
    let results = [];
    results.push(setValidateSelectionBtnState());
    results.push(setDuplicateRecipeBtnState());
    results.push(setEditRecipeBtnState());
    results.push(setDeleteRecipeBtnState());
    results.push(setSaveRecipeBtnState());
    results.push(setViewInHomepageBtnState());
    results.push(setExcludeDatalogBtnState());
    results.push(setAppendSelectedTraceabilityTestsBtnState());
    results.push(setDeleteTraceabilityTestsBtnState());
    results.push(setAddTestsBtnState());
    results.push(setRemoveTestBtnState());
    results.push(setClearAllVariablesBtnState());
    results.push(setViewTestRawDataDropdownItemState());
    results.push(setEditMetadataOutputBtnState());
    results.push(setDeleteMetadataOutputBtnState());
    results.push(setDeleteDatalogBtnState());
    results.push(setEditDatalogBtnState());
    results.push(setReprocessDatalogBtnState());
    results.push(setSaveTableDataBtnState());
    results.push(setCancelEditTableDataBtnState());
    results.push(setPercentDriftOptionState());
    // Enable Analyse Parts only when there is a selection in the Part ID table (dice analysis)
    results.push(setAnalysePartsBtnState());
    let toolbarPropsCopy = Helper.cloneObject(toolbarProps);
    results.forEach((result) => {
      if (!toolbarPropsCopy[result.key]) {
        toolbarPropsCopy[result.key] = {};
      }
      toolbarPropsCopy[result.key].disabled = result.value;
      if (result.tooltip !== undefined) {
        toolbarPropsCopy[result.key].tooltip = result.tooltip;
      }
      toolbarPropsCopy[result.key].visibility = result.visibility;
    });
    setToolbarProps(toolbarPropsCopy);
  };

  /**
   * Set Analyse Parts button state (enabled only when Part ID selection exists)
   *
   * @returns {object}
   */
  const setAnalysePartsBtnState = () => {
    const key = "analyseParts";
    const disabled = !(gridSelectionData[selectionStoreKey]?.length > 0);
    return { key, value: disabled };
  };

  /**
   * Initialize datasource to get grid data
   */
  const initGridDatasource = () => {
    // create datasource
    const datasource = createServerSideDatasource();
    // register the datasource with the grid
    gridRef.current.api.setGridOption("serverSideDatasource", datasource);
  };

  /**
   * Used by the SSRM to fetch rows for the grid
   *
   * @returns {object}
   */
  const createServerSideDatasource = () => {
    return {
      getRows: (requestParams) => {
        console.log(
          "[Datasource] - rows requested by grid: ",
          requestParams.request,
        );

        requestParams = formatGridRequestParams(requestParams);

        // apply unique column filter on row group breakdown if unique group column is set
        if (
          Array.isArray(requestParams.request.groupKeys) &&
          requestParams.request.groupKeys.length > 0 &&
          component.table_unique_group_cols
        ) {
          component.table_unique_group_cols.forEach((colKey) => {
            if (requestParams.request.groupKeys.indexOf(colKey) === -1) {
              requestParams.request.rowGroupCols.push(colKey);
              requestParams.request.groupKeys.push(
                requestParams.parentNode.data[colKey],
              );
            }
          });
        }

        setGridRequestParams(requestParams);
        setShouldGetGridData(true);
      },
    };
  };

  /**
   * Format grid request parameters for API request
   * Convert rowGroupCols from array of group column data objects to array of group column ids
   *
   * @param {object} requestParams
   * @returns {object} requestParams
   */
  const formatGridRequestParams = (requestParams) => {
    requestParams.request.rowGroupCols = requestParams.request.rowGroupCols.map(
      (groupCol) => {
        return groupCol.id;
      },
    );
    requestParams.request.sortModel = requestParams.request.sortModel.filter(
      (sortModel) => {
        return sortModel.colId !== "ag-Grid-AutoColumn";
      },
    );

    return requestParams;
  };

  /**
   * Show loading overlay
   */
  const showLoadingOverlay = () => {
    if (gridRef.current.api) {
      gridRef.current.api.setGridOption("loading", true);
    }
  };

  /**
   * Hide loading overlay
   */
  const hideLoadingOverlay = () => {
    if (gridRef.current.api) {
      gridRef.current.api.setGridOption("loading", false);
    }
  };

  /**
   * Update grid height to fill remaining space of the page
   */
  const fillGridHeight = () => {
    const gridContainer =
      gridWrapperRef.current.getElementsByClassName("ag-root-wrapper")[0];
    const contentStyle = window.getComputedStyle(mainContentRef);
    const contentRect = mainContentRef.getBoundingClientRect();
    const height =
      contentRect.height -
      gridContainer.getBoundingClientRect().top -
      parseFloat(contentStyle.paddingBottom);
    gridRef.current.api.setGridOption("domLayout", "normal");
    setGridHeight(`${height}px`);
  };

  /**
   * Update grid height based on maximum number of rows to show
   *
   * @param {int} maxRows
   */
  const updateGridHeight = (maxRows) => {
    if (gridRef.current.api.getDisplayedRowCount() > maxRows) {
      const header =
        gridWrapperRef.current.getElementsByClassName("ag-header")[0];
      // add scrollbar height which is 15
      let height = (header.clientHeight ?? 0) + 15;

      if (isServerSide()) {
        gridRef.current.api.forEachNode((node, index) => {
          if (index < maxRows) {
            height += node.rowHeight;
          }
        });
      } else {
        gridRef.current.api.forEachNodeAfterFilterAndSort((node, index) => {
          if (index < maxRows) {
            height += node.rowHeight;
          }
        });
      }
      gridRef.current.api.setGridOption("domLayout", "normal");
      setGridHeight(`${height}px`);
    } else {
      gridRef.current.api.setGridOption("domLayout", "autoHeight");
      setGridHeight("auto");
    }
  };

  /**
   * Update grid pagination based on number of displayed rows
   *
   * @param {int} maxRows
   */
  const updateGridPagination = (maxRows) => {
    setPagination(gridRef.current.api.getDisplayedRowCount() > maxRows);
  };

  /**
   * Debounced getting of grid data
   */
  const delayGetGridData = useCallback(
    debounce((params, requestParams) => {
      getGridData(params, requestParams);
    }, 1000),
    [],
  );

  /**
   * Get grid data
   *
   * @param {object} params - grid api endpoint parameters
   * @param {object} requestParams - grid request parameters
   * @param {string} quickFilter - text to be searched on visible columns of grid
   * @returns {AbortController}
   */
  const getGridData = (params, requestParams, quickFilter) => {
    console.log("=== YH GRID GET DATA DEBUG ===");
    console.log("Grid component name:", component.name);
    console.log("Grid component reload_grid_key:", component.reload_grid_key);
    console.log(
      "Available reloadGridFilters keys:",
      Object.keys(reloadGridFilters),
    );
    console.log("ReloadGridFilters content:", reloadGridFilters);

    showLoadingOverlay();

    const pageFilters = Helper.getPageFilters(filters, pageKey);
    let allFilters = { ...pageFilters, ...gridFilters, ...prerenderData };

    console.log("Initial allFilters:", allFilters);
    console.log("Looking for filters under key:", component.reload_grid_key);
    console.log("Found filters:", reloadGridFilters[component.reload_grid_key]);

    if (reloadGridFilters[component.reload_grid_key]) {
      allFilters = {
        ...allFilters,
        ...reloadGridFilters[component.reload_grid_key],
      };
      console.log("Applied reload grid filters, new allFilters:", allFilters);
    } else {
      console.log("No reload grid filters found for this component");
    }

    let abortCtl = Api.initAbortCtl();
    const url =
      typeof params.url_endpoint === "string"
        ? Helper.parseUrlEndpoint(params.url_endpoint, allFilters)
        : "";
    const paramKeys = Object.keys(
      merge(params.body_params ?? {}, params.query_params ?? {}),
    );
    const queryParams =
      paramKeys.length > 0
        ? Helper.filterObjectByKeys(allFilters, paramKeys)
        : allFilters;

    console.log("Final queryParams:", queryParams);
    console.log("Param keys:", paramKeys);
    console.log("URL:", url);

    if (url) {
      let payload = {
        stream: stream === "1" ? 1 : 0,
        ...queryParams,
        grid_request_params: requestParams ? requestParams.request : {},
      };

      console.log("Final payload being sent:", payload);
      console.log("=== END YH GRID DEBUG ===");
      if (quickFilter) {
        payload.q = quickFilter;
        const displayedColumnIds = gridRef.current.api
          .getAllDisplayedColumns()
          .map((column) => {
            return column.colId;
          });
        const columns = userColumnDefs.current
          ?.filter((columnDef) => {
            return (
              columnDef?.filterProps?.hasDBField &&
              displayedColumnIds.indexOf(columnDef.field) !== -1
            );
          })
          .map((column) => {
            return column.field;
          });
        payload.columns = columns.join();
      }

      abortCtl = Api.getData(
        url,
        params.method,
        (res) => {
          if (res.success) {
            userColumnDefs.current = Helper.cloneObject(res.data.col_def);
            updateColumns(
              res.data.col_def,
              settings.apply_user_settings ?? true,
            );
            if (isServerSide()) {
              // supply rows for requested block to grid
              if (res?.data?.loading) {
                delayGetGridData(params, requestParams);
              } else {
                requestParams.success({
                  rowData: res.data.table_data,
                  rowCount: res.data.total_rows,
                });
              }
            } else {
              if (res?.data?.loading) {
                delayGetGridData(params, requestParams);
              } else {
                setRowData(res.data.table_data);
              }
            }
            setHasLoaded(true);

            if (typeof eventHandlers.handlePostReload === "function") {
              eventHandlers.handlePostReload(res.data);
            }

            if (res.data.detail_grid_component) {
              const detailGridComponent = res.data.detail_grid_component;
              detailGridComponents[detailGridComponentsKey][component.name] =
                detailGridComponent;
              gridRef.current.api.setGridOption(
                "detailCellRendererParams",
                detailCellRendererParams(detailGridComponent, gridRef.current),
              );
            }

            if (
              component?.props?.toolbar.some(
                (menu) => menu.key === "showBreakdownPerPin",
              )
            ) {
              updateShowBreakdownPerPinBtn(
                "showBreakdownPerPin",
                res.data.table_data,
              );
            }
          } else {
            if (isServerSide()) {
              requestParams.fail();
            }
            notification.warning({
              message: component.display_name,
              description: res.message,
            });
          }

          res?.data?.loading ? showLoadingOverlay() : hideLoadingOverlay();
        },
        (err) => {
          if (isServerSide()) {
            requestParams.fail();
          }
          notification.error({
            message: component.display_name,
            description: err,
          });
          hideLoadingOverlay();
        },
        payload,
        params.body_params,
      );
    } else if (params.data) {
      setRowData(params.data);
      hideLoadingOverlay();
    }

    return abortCtl;
  };

  /**
   * Get detail grid data
   *
   * @param {object} detailGridComponent
   * @param {object} masterRowParams - master row parameters
   */
  const getDetailGridData = (detailGridComponent, masterRowParams) => {
    const pageFilters = Helper.getPageFilters(filters, pageKey);
    let allFilters = { ...pageFilters, ...gridFilters, ...prerenderData };

    let queryParams = detailGridComponent.props.params.query_params
      ? Helper.filterObjectByKeys(
          allFilters,
          detailGridComponent.props.params.query_params,
        )
      : allFilters;

    if (detailGridComponent.props.params.detail_grid_params) {
      const detailGridParams =
        detailGridComponent.props.params.detail_grid_params;
      Object.keys(detailGridParams).forEach((paramKey) => {
        const queryParam =
          detailGridParams[paramKey].value !== undefined
            ? detailGridParams[paramKey].value
            : detailGridParams[paramKey].row_data_key &&
                masterRowParams.data[detailGridParams[paramKey].row_data_key]
              ? detailGridParams[paramKey].type === "string"
                ? masterRowParams.data[
                    detailGridParams[paramKey].row_data_key
                  ].toString()
                : masterRowParams.data[detailGridParams[paramKey].row_data_key]
              : null;
        if (queryParam !== null) {
          queryParams[paramKey] = queryParam;
        }
      });
    }

    const url =
      typeof detailGridComponent.props.params.url_endpoint === "string"
        ? Helper.parseUrlEndpoint(
            detailGridComponent.props.params.url_endpoint,
            merge(allFilters, queryParams),
          )
        : "";

    if (url) {
      let payload = {
        stream: stream === "1" ? 1 : 0,
        ...queryParams,
      };

      Api.getData(
        url,
        detailGridComponent.props.params.method,
        (res) => {
          if (res.success) {
            const detailColumnDefs = generateDetailColumnDefinitions(
              detailGridComponent,
              res.data.col_def,
            );
            masterRowParams.node.detailGridInfo.api.setGridOption(
              "columnDefs",
              detailColumnDefs,
            );
            masterRowParams.successCallback(res.data.table_data);

            if (res.data.detail_grid_component) {
              const detailGridDetailComponent = res.data.detail_grid_component;
              detailGridComponents[detailGridComponentsKey][
                detailGridComponent.name
              ] = detailGridDetailComponent;
              masterRowParams.node.detailGridInfo.api.setGridOption(
                "detailCellRendererParams",
                detailCellRendererParams(
                  detailGridDetailComponent,
                  masterRowParams.node.detailGridInfo,
                ),
              );
            }
          } else {
            notification.warning({
              message: component.display_name,
              description: res.message,
            });
          }
        },
        (err) => {
          notification.error({
            message: component.display_name,
            description: err,
          });
        },
        payload,
        detailGridComponent.props.params.body_params,
      );
    }
  };

  /**
   * Get page size options
   *
   * @returns {array} sizeOptions
   */
  const getPageSizeOptions = () => {
    const sizes = [10, 25, 50, 100, 200, 400, 500];
    const sizeOptions = [];
    sizes.forEach((size) => {
      sizeOptions.push({
        value: size,
        label: `${size} / page`,
      });
    });

    return sizeOptions;
  };

  /**
   * Set row grouping
   */
  const setRowGrouping = (rowGroups) => {
    let state = [];
    gridRef.current.api.getColumns().forEach((column) => {
      state.push({
        colId: column.colId,
        rowGroup: rowGroups.indexOf(column.colId) !== -1,
        // set aggFunc to trigger sorting when grouped
        // function value won't matter since calculation is done on server side
        aggFunc: "sum",
      });
    });
    gridRef.current.api.applyColumnState({
      state: state,
    });
  };

  /**
   * Set if column order should be maintained or not when updating column definitions
   *
   * @param {boolean} value
   */
  const setMaintainColumnOrder = (value) => {
    gridRef.current.api.setGridOption("maintainColumnOrder", value);
  };

  /**
   * Get component key of grid websocket
   *
   * @returns {string}
   */
  const getGridSocketComponentKey = () => {
    return Helper.generateLabelByData(component.websockets?.component_key, {
      ...filters[pageKey],
      ...gridFilters,
      ...prerenderData,
    });
  };

  /**
   * The grid has initialised and is ready for most api calls,
   * but may not be fully rendered yet
   */
  const onGridReady = () => {
    const renderedComponentsCopy = { ...renderedComponents };
    renderedComponentsCopy[pageKey] = [
      ...(renderedComponentsCopy[pageKey] || []),
      component.name,
      ...(component.websockets?.component_key
        ? [getGridSocketComponentKey()]
        : []),
    ];
    setRenderedComponents(renderedComponentsCopy);
    setIsGridReady(true);

    isServerSide() ? initGridDatasource() : setShouldGetGridData(true);
    if (rowGroups) {
      setRowGrouping(rowGroups);
    }
    if (pagination !== false) {
      // move paging panel for custom controls
      const pagingPanel =
        gridWrapperRef.current.getElementsByClassName("ag-paging-panel")[0];
      gridPagingPanel.current.appendChild(pagingPanel);
    }
    updateToolbarButtonState();
  };

  /**
   * Fired the first time data is rendered into the grid
   *
   * @param {FirstDataRenderedEvent} event
   */
  const onFirstDataRendered = (event) => {
    /**
     * The autosizeColumns method might not work as expected if called within the onFirstDataRendered event.
     * This can be due to the fact that the grid might not have fully rendered or calculated column sizes by the time onFirstDataRendered is triggered.
     * Delaying the autosizing slightly using setTimeout is the only way for now
     */
    if (!hasUserSettings) {
      setTimeout(() => {
        autoSizeColumns();
      }, 0);
    } else if (autoSizeColumnDefs.length > 0) {
      setTimeout(() => {
        autoSizeColumns(autoSizeColumnDefs);
      }, 0);
    }
    setMaintainColumnOrder(true);

    if (settings.height === "auto" && settings.max_rows !== undefined) {
      // adding a timeout to wait for the cell renderer to adjust row height
      setTimeout(() => {
        updateGridHeight(settings.max_rows);
        if (settings.pagination !== false) {
          updateGridPagination(settings.max_rows);
        }
      }, 1000);
    }
    if (settings.fill_height) {
      fillGridHeight();
    }

    if (onFirstDataRenderedActions.length > 0) {
      onFirstDataRenderedActions.forEach((action) => {
        action.fn(action.params);
      });
    }
    // Initialize dice analysis boxplot with current page on first render
    try {
      if (!isServerSide() || (gridRequestParams && gridRequestParams.request)) {
        onPaginationChanged({});
      }
    } catch (err) {
      void err; // ignore
    }
    if (typeof eventHandlers.onFirstDataRendered === "function") {
      eventHandlers.onFirstDataRendered(event);
    }
  };

  /**
   * A column, or group of columns, was hidden / shown
   *
   * @param {ColumnVisibleEvent} event
   */
  const onColumnVisible = (event) => {
    // update column visibility when showing/hiding column from columns tool panel
    // this is needed to persist the change when sorting and other column actions
    if (event.source === "toolPanelUi") {
      const displayedColumnIds = event.api
        .getAllDisplayedColumns()
        .map((column) => {
          return column.colId;
        });
      columnDefs.forEach((columnDef) => {
        columnDef.hide = displayedColumnIds.indexOf(columnDef.field) === -1;
      });
    }
    if (settings.apply_user_settings !== false) {
      saveUserColumnState();
    }
  };

  /**
   * A column was moved.
   *
   * @param {ColumnMovedEvent} event
   */
  const onColumnMoved = (event) => {
    if (event.finished && settings.apply_user_settings !== false) {
      saveUserColumnState();
    }
  };

  /**
   * A column was resized.
   *
   * @param {ColumnResizedEvent} event
   */
  const onColumnResized = (event) => {
    if (event.finished && settings.apply_user_settings !== false) {
      saveUserColumnState();
    }
  };

  /**
   * A column, or group of columns, was pinned / unpinned.
   */
  const onColumnPinned = () => {
    if (settings.apply_user_settings !== false) {
      saveUserColumnState();
    }
  };

  /**
   * Triggers when the list of displayed columns changed
   * This can result from columns open / close, column move, pivot, group, etc.
   *
   * @param {DisplayedColumnsChangedEvent} event
   */
  const onDisplayedColumnsChanged = (event) => {
    updateNoRowsOverlayDisplay(event);
  };

  /**
   * Show/Hide no rows overlay based on number of displayed rows
   *
   * @param {object} gridOptions
   */
  const updateNoRowsOverlayDisplay = (gridOptions) => {
    gridOptions.api.getDisplayedRowCount() === 0
      ? gridOptions.api.showNoRowsOverlay()
      : gridOptions.api.setGridOption("loading", false);
  };

  /**
   * Save column state to user settings
   */
  const saveUserColumnState = () => {
    let state = getColumnState();

    // do not save column props that might change
    state = state.filter((column) => {
      dynamicColumnDefs.forEach((colDef) => {
        delete column[colDef];
      });
      return column.colId !== "ag-Grid-SelectionColumn";
    });
    // Apply the state so that it will be retained during rerenders
    applyColumnState(state);

    Helper.setUserSettings(
      UserSettingsKeys.grid_column_state,
      state,
      "grid",
      component,
      getUserSettingsFilters(),
    );
  };

  /**
   * Get user settings filters
   *
   * @returns {object} userSettingsFilters
   */
  const getUserSettingsFilters = () => {
    const userSettingsFilters =
      component.name === ComponentNameMapper.home_search_table
        ? { rowGroups: rowGroups }
        : Helper.getPageFilters(filters, pageKey);

    return userSettingsFilters;
  };

  /**
   * Handle search input change
   *
   * @param {onChangeEvent} event
   */
  const handleSearch = (event) => {
    setSearched(event.target.value);
    debouncedSearchData(event.target.value);
  };

  /**
   * Search for grid data
   *
   * @param {string} searchedValue
   */
  const debouncedSearchData = useCallback(
    debounce((searchedValue) => {
      setQuickFilter(searchedValue);
      if (isServerSide()) {
        gridRef.current.api.refreshServerSide({ purge: true });
      } else {
        gridRef.current.api.setGridOption("quickFilterText", searchedValue);
      }
    }, 500),
    [],
  );

  /**
   * Reset table filters and sorting
   */
  const resetTable = () => {
    resetQuickFilter();
    gridRef.current.api.setFilterModel(null);
    gridRef.current.api.resetColumnState();
    // set row grouping if present to apply agg func to enable sorting
    if (rowGroups) {
      setRowGrouping(rowGroups);
    }
    if (gridFilterForm) {
      gridFilterForm.resetFields();
      const allFilterFields = [
        ...new Set([
          ...Object.keys(gridFilterFields),
          ...Object.keys(gridFilterForm.getFieldsValue()),
        ]),
      ];
      allFilterFields.forEach((fieldKey) => {
        delete filters[pageKey][fieldKey];
      });
    }
  };

  /**
   * Reset quick filter
   */
  const resetQuickFilter = () => {
    setSearched();
    setQuickFilter();
    gridRef.current.api.resetQuickFilter();
    if (isServerSide()) {
      gridRef.current.api.refreshServerSide({ purge: true });
    } else {
      gridRef.current.api.setGridOption("quickFilterText", "");
    }
  };

  /**
   * Sets columns to adjust in size to fit the header and cell content
   */
  const adjustColumnsEvenly = () => {
    const horizonalScrollbar = gridWrapperRef.current.getElementsByClassName(
      "ag-body-horizontal-scroll-viewport",
    )[0];
    if (horizonalScrollbar && horizonalScrollbar.offsetHeight > 0) {
      autoSizeColumns();
    } else {
      fitColumnsToGrid();
    }
  };

  /**
   * Auto-sizes columns based on their header and content
   *
   * @param {array} componentColumnDefs
   */
  const autoSizeColumns = (componentColumnDefs) => {
    const columns = Array.isArray(componentColumnDefs)
      ? componentColumnDefs.map((column) => {
          return column.field;
        })
      : gridRef.current.api
          .getColumns()
          .filter((column) => {
            const userColumn = Helper.filterArrayAndFind(
              userColumnDefs.current,
              "field",
              column.colId,
            );
            return userColumn && userColumn.width === undefined;
          })
          .map((column) => {
            return column.colId;
          });
    gridRef.current.api.autoSizeColumns(columns);
  };

  /**
   * Resize columns to fit the width of the grid
   */
  const fitColumnsToGrid = () => {
    gridRef.current.api.sizeColumnsToFit({
      defaultMinWidth: 100,
    });
  };

  /**
   * Display filter table options
   */
  const showFilterTableOptions = () => {
    setIsFilterTableModalOpen(true);
  };

  /**
   * Display form to add a new team
   */
  const showAddNewTeamForm = () => {
    setIsAddNewTeamModalOpen(true);
  };

  /**
   * Display form to filter tests
   */
  const showTestsFilterForm = () => {
    setIsTestsFilterModalOpen(true);
  };

  /**
   * Display form to filter test table tests
   */
  const showTestTableFilterForm = () => {
    setIsTestTableFilterModalOpen(true);
  };

  /**
   * Display form to add tests
   */
  const showAddTestsForm = () => {
    setIsAddTestsModalOpen(true);
  };

  /*
   * Remove calculated test
   * */
  const removeTest = () => {
    if (typeof eventHandlers.onRemoveTest === "function") {
      const isServerSide =
        gridRef.current?.api.getGridOption("rowModelType") === "serverSide";
      let selectedNodes = GridHelper.getSelectedNodes(
        gridRef.current,
        isServerSide,
      );
      eventHandlers.onRemoveTest(
        selectedNodes,
        component.table_unique_cols,
        deleteTableRow,
      );
    }
  };

  /*
   * Clear tests variable
   * */
  const clearAllVariables = () => {
    const isServerSide =
      gridRef.current?.api.getGridOption("rowModelType") === "serverSide";
    let selectedNodes = GridHelper.getSelectedNodes(
      gridRef.current,
      isServerSide,
    );
    selectedNodes.forEach((node) => {
      node.setDataValue("variable", null);
    });
    if (isServerSide) {
      gridRef.current?.api.setServerSideSelectionState({
        selectAll: false,
        toggledNodes: [],
      });
    } else {
      gridRef.current?.api.deselectAll();
    }
    if (typeof eventHandlers.onClearAllVariables === "function") {
      eventHandlers.onClearAllVariables(selectedNodes);
    }
  };

  /**
   * Display followed tests only
   *
   * @param {CheckboxChangeEvent} e
   */
  const showFollowedTestsOnly = (e) => {
    let gridFiltersCopy = Helper.cloneObject(gridFilters);
    gridFiltersCopy.show_followed = e.target.checked;
    setGridFilters(gridFiltersCopy);
    reloadData();
  };

  /**
   * Show breakdown per pin number
   *
   * @param {CheckboxChangeEvent} e
   */
  const showBreakdownPerPin = (e) => {
    let gridFiltersCopy = Helper.cloneObject(gridFilters);
    gridFiltersCopy.per_pin = e.target.checked;
    setGridFilters(gridFiltersCopy);
    reloadData();
  };

  /**
   * Show notification message saying that this is for development
   */
  const notifyForDevelopment = () => {
    notification.warning({
      message: "Coming Soon!",
      description: "This is still for development.",
    });
  };

  /**
   * Toggle wafer id breakdown table display state
   */
  const toggleShowWaferIdBreakdownTable = () => {
    setIsVisible(!isVisible);
  };

  /**
   * Sort yield trend chart based on grid sorting
   */
  const sortYieldTrendChart = () => {
    const trendChartKeys =
      chartKeys[pageKey][ComponentNameMapper.lot_yield_trend_chart];
    if (Array.isArray(trendChartKeys)) {
      let sortedCategories = [];
      gridRef.current.api.forEachNodeAfterFilterAndSort((node) => {
        sortedCategories.push(node.data.lot_id);
      });
      const sorting = GridHelper.getSorting(gridRef.current);

      trendChartKeys.forEach((chartKey) => {
        reloadChartFilters[chartKey].sorting = sorting;
        reloadChartFilters[chartKey].sorted_categories = sortedCategories;
        if (reloadGridFilters[component.reload_grid_key]?.lot_count) {
          reloadChartFilters[chartKey].lot_count =
            reloadGridFilters[component.reload_grid_key].lot_count;
        }
        chartComponentRefs[chartKey]?.current.reloadChart();
      });
    }
  };

  /**
   * Update condition value after edit
   *
   * @param {CellEditingStoppedEvent} event
   */
  const updateConditionValue = (event) => {
    const tableUniqueCols = component.table_unique_cols ?? [];
    initialGridFilters.table_data = unionBy(
      [event.data],
      initialGridFilters.table_data,
      (item) => {
        return tableUniqueCols.map((key) => item[key]).join("_");
      },
    );
  };

  /**
   * Update column value of NPI Test Summary table after edit
   *
   * @param {CellEditingStoppedEvent} event
   */
  const updateNpiTestSummaryTableColumnValue = (event) => {
    const params = {
      grid_table_name: component.name,
      report_key: urlParams[pageKey].report_key,
      recipe_key: prerenderData.recipe_key,
      analysis_set_key: prerenderData.analysis_set_key,
      test_number: event.data.test_number,
      field: event.column.colId,
      value: event.value,
    };
    Api.updateNpiTestSummaryTableColumnValue(
      (res) => {
        if (res.success) {
          message.success(res.message, 5);
        } else {
          message.warning(res.message, 10);
        }
      },
      (err) => {
        message.error(err, 10);
      },
      params,
    );
  };

  /**
   * Validate or invalidate selected datalogs
   */
  const validateSelection = () => {
    const dsk = gridSelectionData[selectionStoreKey].map((data) => {
      return data.data_struc_key;
    });
    if (isValidateSelection) {
      validateDatalog(dsk, isValidateSelection);
    } else {
      showInvalidateDatalogConfirm(dsk, isValidateSelection);
    }
  };

  /**
   * Show confirmation to invalidate datalog
   *
   * @param {array} dsk
   * @param {boolean} isValidateSelection
   */
  const showInvalidateDatalogConfirm = (dsk, isValidateSelection) => {
    confirm({
      title: "Confirm Invalidation",
      icon: <ExclamationCircleOutlined />,
      content: "Are you sure you want to invalidate selected datalog(s)?",
      okText: "Invalidate",
      cancelText: "Cancel",
      onOk() {
        validateDatalog(dsk, isValidateSelection);
      },
    });
  };

  /**
   * Validate/Invalidate datalog(s)
   *
   * @param {array} dsk
   * @param {boolean} isValidateSelection
   */
  const validateDatalog = (dsk, isValidateSelection) => {
    Api.validateDatalog(
      (res) => {
        if (res.success) {
          message.success(res.message);
          reloadData();
        } else {
          message.warning(res.message, 10);
        }
      },
      (err) => {
        message.error(err, 10);
      },
      {
        dsk: dsk.join(),
        valid: isValidateSelection ? 1 : 0,
      },
    );
  };

  /**
   * Show/Hide invalid datalogs
   */
  const toggleShowInvalid = () => {
    setIsShowInvalid(!isShowInvalid);
  };

  /**
   * Update show invalid button type, label, and icon
   */
  const updateShowInvalidBtn = () => {
    const key = "showInvalid";
    let toolbarPropsCopy = Helper.cloneObject(toolbarProps);
    if (!toolbarPropsCopy[key]) {
      toolbarPropsCopy[key] = {};
    }
    toolbarPropsCopy[key].type = isShowInvalid ? "primary" : "default";
    toolbarPropsCopy[key].label = {
      label: isShowInvalid ? "Hide Invalid" : "Show Invalid",
    };
    toolbarPropsCopy[key].icon = isShowInvalid
      ? "EyeInvisibleOutlined"
      : "EyeOutlined";
    toolbarPropsCopy[key].disabled =
      Array.isArray(rowGroups) && rowGroups[0] !== "datalog";

    setToolbarProps(toolbarPropsCopy);
  };

  /**
   * Apply show invalid filter
   */
  const applyShowInvalidFilter = () => {
    let gridFiltersCopy = Helper.cloneObject(gridFilters);
    gridFiltersCopy.show_invalid = isShowInvalid;
    setGridFilters(gridFiltersCopy);
    reloadData();
  };

  /**
   * Update show wafer id breakdown table button type, label, and icon
   */
  const updateShowWaferIdBreakdownTableBtn = () => {
    const key = "showWaferIdBreakdownTable";
    let btnTitle = "Wafer ID Breakdown Table";
    let title = "Breakdown of Wafer IDs";

    const pageFilters = Helper.getPageFilters(filters, pageKey);
    if (pageFilters.mfg_process === "Test") {
      btnTitle = "Final Test Stages Breakdown Table";
      title = "Breakdown of Final Test Stages";
    }
    setGridTitle(title);

    let menuPropsCopy = Helper.cloneObject(menuProps);
    if (!menuPropsCopy[key]) {
      menuPropsCopy[key] = {};
    }
    menuPropsCopy[key].type = isVisible ? "primary" : "default";
    menuPropsCopy[key].label = isVisible
      ? `Hide ${btnTitle}`
      : `Show ${btnTitle}`;
    menuPropsCopy[key].icon = isVisible
      ? "EyeInvisibleOutlined"
      : "EyeOutlined";
    setMenuProps(menuPropsCopy);
  };

  /**
   * Download grid data in CSV format
   *
   * @param {string} loadingStatusKey
   */
  const downloadTableAsCSV = (loadingStatusKey) => {
    Helper.updateLoadingStatus(
      loadingStatus,
      setLoadingStatus,
      loadingStatusKey,
      true,
    );

    if (rowModelType === "serverSide") {
      const exportColumnIds = getExportColumns()
        .map((column) => {
          return column.colId;
        })
        .join();
      const selectedNodes = GridHelper.getServerSideSelectedNodes(
        gridRef.current,
      );
      const selectedKeys = GridHelper.getSelectedKeys(
        selectedNodes,
        gridRef.current,
        component,
      );
      const exportParams = {
        columns: exportColumnIds,
        grid_request_params: {
          rowGroupCols: selectedKeys.groups,
          selectedGroupKeys: selectedKeys.groupKeys,
          selectedGroupUniqueKeys: selectedKeys.groupUniqueKeys,
          selectedKeys: selectedKeys.selectedKeys,
        },
      };

      const pageFilters = Helper.getPageFilters(filters, pageKey);
      let allFilters = { ...pageFilters, ...gridFilters, ...prerenderData };
      if (quickFilter) {
        allFilters.q = quickFilter;
      }

      GridHelper.downloadTableAsCSV(
        Helper.parseUrlEndpoint(
          component.download.csv.url_endpoint,
          allFilters,
        ),
        `${component.download.csv.filename}-${+new Date()}.csv`,
        exportParams,
        allFilters,
        loadingStatus,
        setLoadingStatus,
        loadingStatusKey,
      );
    } else {
      const exportParams = {
        columnKeys: getExportColumns(),
        prependContent: getExportPrependContent(),
      };
      gridRef.current.api.exportDataAsCsv(exportParams);
      Helper.updateLoadingStatus(
        loadingStatus,
        setLoadingStatus,
        loadingStatusKey,
        false,
      );
    }
  };

  /**
   * Get content to put at the top of the file export
   * Applicable to client side grid only
   *
   * @returns {array} content
   */
  const getExportPrependContent = () => {
    let content = [];
    const pageFilters = filters[pageKey];
    if (component.download?.csv?.prepend_content_keys) {
      Object.keys(component.download.csv.prepend_content_keys).forEach(
        (key) => {
          if (pageFilters[key]) {
            content.push([
              {
                data: {
                  value: `${component.download.csv.prepend_content_keys[key]}: ${pageFilters[key]}`,
                  type: "String",
                },
              },
            ]);
          }
        },
      );
    }
    if (content.length > 0) {
      content.push([]);
    }
    return content;
  };

  /**
   * Get columns to be exported
   *
   * @returns {array} exportColumns
   */
  const getExportColumns = () => {
    const excludeColumns = ["ag-Grid-AutoColumn", "checkbox", "quick_actions"];
    const exportColumns = gridRef.current.api
      .getAllDisplayedColumns()
      .filter((column) => {
        return excludeColumns.indexOf(column.colId) === -1;
      });

    return exportColumns;
  };

  /**
   * Open columns tool panel
   */
  const openColumnsToolPanel = () => {
    gridRef.current.api.openToolPanel("columns");
  };

  /**
   * Triggers when a menu item is clicked
   *
   * @param {object} object containing clicked menu item key value
   */
  const onMenuItemClick = ({ key }, loadingStatusKey) => {
    switch (key) {
      case "add_label":
        setIsAddLabelModalOpen(true);
        break;
      case "filter_by_label":
        setIsFilterByLabelModalOpen(true);
        break;
      case "create_new_label":
        setIsCreateNewLabelModalOpen(true);
        break;
      case "download_table":
      case "export_csv":
        downloadTableAsCSV(loadingStatusKey);
        break;
      case "add_remove_column":
        openColumnsToolPanel();
        break;
      case "save_table_settings":
        setIsSaveTableSettingsModalOpen(true);
        break;
      case "load_saved_columns":
        setIsLoadTableSettingsModalOpen(true);
        break;
      case "reset_columns":
        showResetColumnsConfirm();
        break;
      case "add_parametric_column":
        setIsAddParametricColumnModalOpen(true);
        break;
      case "add_bin_column":
        setIsAddBinColumnModalOpen(true);
        break;
      case "entire_page":
        selectPage();
        break;
      case "range":
        selectRange();
        break;
      case "invert_selection":
        invertSelection();
        break;
      case "clear_selection":
        clearSelection();
        break;
      case "view_test_raw_data":
        viewTestRawData();
        break;
      case "download_datalogs":
        downloadDatalogs({ loadingStatusKey });
        break;
      case "reload_table":
        reloadData();
        break;
      case "reset_table":
        showResetTableConfirm();
        break;
      case "adjust_columns_evenly":
        adjustColumnsEvenly();
        break;
      case "edit_table_data":
        editTableData();
        break;
      case "save_table_data":
        saveTableData();
        break;
      case "cancel_edit_table":
        cancelEditTableData();
        break;
    }
  };

  /**
   * Filter table according to selected status
   *
   * @param {string} value
   */
  const filterTableStatus = (value) => {
    const selectedStatusOptionCopy = Helper.cloneObject(selectedStatusOption);
    selectedStatusOptionCopy[gridId] = value;
    setSelectedStatusOption(selectedStatusOptionCopy);
    let reloadGridFiltersCopy = Helper.cloneObject(reloadGridFilters);
    reloadGridFiltersCopy[component.reload_grid_key].file_status =
      value?.join();
    setReloadGridFilters(reloadGridFiltersCopy);
    setReloadGrids([component.reload_grid_key]);
    setReloadGrid(Date.now());
  };

  /**
   * Filter table according to selected subcon
   *
   * @param {string} value
   */
  const filterTableSubcon = (value) => {
    setSelectedDatalogProcessingSubconOption(value);
    let reloadGridFiltersCopy = Helper.cloneObject(reloadGridFilters);
    reloadGridFiltersCopy[component.reload_grid_key].subcon = value?.join();
    setReloadGridFilters(reloadGridFiltersCopy);
    setReloadGrids([component.reload_grid_key]);
    setReloadGrid(Date.now());
  };

  /**
   * Filter table by recipe type
   *
   * @param {object} valueObj
   */
  const filterByRecipeType = async (valueObj) => {
    if (isServerSide()) {
      let gridFiltersCopy = Helper.cloneObject(gridFilters);
      gridFiltersCopy.type = valueObj.value;
      setGridFilters(gridFiltersCopy);
      reloadData();
    } else {
      const filterModel =
        valueObj.value !== "" && valueObj.value !== null
          ? {
              filterType: "text",
              type: "equals",
              filter: valueObj.label,
            }
          : null;
      await gridRef.current.api.setColumnFilterModel(
        "recipe_category",
        filterModel,
      );
      gridRef.current.api.onFilterChanged();
    }
    Helper.setUserSettings(UserSettingsKeys.recipe_type_filter, valueObj);
  };

  /**
   * Filter table by recipe owner
   *
   * @param {object} valueObj
   */
  const filterByRecipeOwner = async (valueObj) => {
    if (isServerSide()) {
      let gridFiltersCopy = Helper.cloneObject(gridFilters);
      gridFiltersCopy.owner = valueObj.value;
      setGridFilters(gridFiltersCopy);
      reloadData();
    } else {
      const filterModel =
        valueObj.value !== "" && valueObj.value !== null
          ? {
              filterType: "text",
              type: "equals",
              filter: valueObj.label,
            }
          : null;
      await gridRef.current.api.setColumnFilterModel("created_by", filterModel);
      gridRef.current.api.onFilterChanged();
    }
    Helper.setUserSettings(UserSettingsKeys.recipe_owner_filter, valueObj);
  };

  /**
   * Filter table by auto trigger status
   *
   * @param {string} value
   */
  const filterByAutoTriggerStatus = async (value) => {
    if (isServerSide()) {
      let gridFiltersCopy = Helper.cloneObject(gridFilters);
      gridFiltersCopy.auto_trigger = value;
      setGridFilters(gridFiltersCopy);
      reloadData();
    } else {
      const filterModel =
        value !== "" && value !== null
          ? {
              value: value,
            }
          : null;
      await gridRef.current.api.setColumnFilterModel(
        "auto_trigger",
        filterModel,
      );
      gridRef.current.api.onFilterChanged();
    }
    Helper.setUserSettings(
      UserSettingsKeys.recipe_auto_trigger_status_filter,
      value,
    );
  };

  /**
   * Filter table by percent drift value
   *
   * @param {int} value
   */
  const filterByPercentDrift = async (value) => {
    let gridFiltersCopy = Helper.cloneObject(gridFilters);
    gridFiltersCopy.limit_pct = value;
    setGridFilters(gridFiltersCopy);
    reloadData();
  };

  /**
   * Duplicate selected recipe
   */
  const duplicateSelectedRecipe = () => {
    if (gridSelectionData[selectionStoreKey].length === 1) {
      setSelectedRecipeData(gridSelectionData[selectionStoreKey][0]);
      setIsDuplicateRecipeModalOpen(true);
    } else {
      message.warning("Please select a recipe.");
    }
  };

  /**
   * Delete selected row
   *
   * @param {object} toolbarOption
   */
  const deleteSelectedRow = (toolbarOption) => {
    if (gridSelectionData[selectionStoreKey].length > 0) {
      showDeleteSelectedRowConfirm(
        gridSelectionData[selectionStoreKey],
        toolbarOption,
      );
    } else {
      message.warning(`Please select a ${toolbarOption.keyword}.`);
    }
  };

  /**
   * Show confirmation to delete selected row
   *
   * @param {array} selectionData
   * @param {object} toolbarOption
   */
  const showDeleteSelectedRowConfirm = (selectionData, toolbarOption) => {
    confirm({
      title: "Confirm Deletion",
      icon: <ExclamationCircleOutlined />,
      content: (
        <>
          <Paragraph>
            Are you sure you want to delete selected {toolbarOption.keyword}{" "}
            permanently?
          </Paragraph>
          <Paragraph>
            <Text strong>Warning!</Text>{" "}
            <Text>This action cannot be undone!</Text>
          </Paragraph>
        </>
      ),
      okText: "Delete",
      cancelText: "Cancel",
      onOk() {
        const keys = selectionData.map((data) => data[toolbarOption.apiParam]);
        deleteRow(keys, toolbarOption);
      },
    });
  };

  /**
   * Delete row
   *
   * @param {array} keys
   * @param {object} toolbarOption
   */
  const deleteRow = (keys, toolbarOption) => {
    const loadingStatusKey = toolbarOption.loadingStatusKey;
    Helper.updateLoadingStatus(
      loadingStatus,
      setLoadingStatus,
      loadingStatusKey,
      true,
    );
    Api[toolbarOption.deleteApiCallback](
      (res) => {
        if (res.success) {
          message.success(res.message);
          reloadData();
        } else {
          message.warning(res.message, 10);
        }
        Helper.updateLoadingStatus(
          loadingStatus,
          setLoadingStatus,
          loadingStatusKey,
          false,
        );
      },
      (err) => {
        message.error(err, 10);
        Helper.updateLoadingStatus(
          loadingStatus,
          setLoadingStatus,
          loadingStatusKey,
          false,
        );
      },
      {
        [toolbarOption.apiParam]: keys.join(),
      },
    );
  };

  /**
   * Save selected recipe
   */
  const saveSelectedRecipe = () => {
    editSelectedRecipe();
  };

  /**
   * Edit selected recipe
   */
  const editSelectedRecipe = () => {
    if (gridSelectionData[selectionStoreKey].length > 0) {
      let params = {};
      switch (gridSelectionData[selectionStoreKey][0].recipe_category) {
        case "Traceability":
        case "Metadata":
          params = {
            recipe_name: gridSelectionData[selectionStoreKey][0].recipe_name,
          };
          if (gridSelectionData[selectionStoreKey][0].version) {
            params.recipe_version =
              gridSelectionData[selectionStoreKey][0].version;
          }
          if (gridSelectionData[selectionStoreKey][0].recipe_data.source_dsks) {
            params.dsk =
              gridSelectionData[selectionStoreKey][0].recipe_data.source_dsks;
          }
          if (gridSelectionData[selectionStoreKey][0].recipe_data.mfg_process) {
            params.mfg_process =
              gridSelectionData[selectionStoreKey][0].recipe_data.mfg_process;
          }
          Helper.renderRecipePage(
            gridSelectionData[selectionStoreKey][0].recipe_category_value,
            params,
            queryClient,
          );
          break;
        case "Consolidation":
          setConsolidationRecipeParams({
            recipe_name: gridSelectionData[selectionStoreKey][0].recipe_name,
            recipe_version: gridSelectionData[selectionStoreKey][0].version,
          });
          break;
      }
    } else {
      message.warning("Please select a recipe.");
    }
  };

  /**
   * Display add new user form
   */
  const showAddNewUserForm = () => {
    setIsAddNewUserModalOpen(true);
  };

  /**
   * Filter table by user type
   *
   * @param {string} value
   */
  const filterByUserType = async (value) => {
    if (isServerSide()) {
      let gridFiltersCopy = Helper.cloneObject(gridFilters);
      gridFiltersCopy.type = value;
      setGridFilters(gridFiltersCopy);
      reloadData();
    } else {
      await gridRef.current.api.setColumnFilterModel("user_type", {
        filterType: "text",
        type: "equals",
        filter: value,
      });
      gridRef.current.api.onFilterChanged();
    }
  };

  /**
   * Filter table by online status
   *
   * @param {string} value
   */
  const filterByOnlineStatus = async (value) => {
    if (isServerSide()) {
      let gridFiltersCopy = Helper.cloneObject(gridFilters);
      gridFiltersCopy.type = value;
      setGridFilters(gridFiltersCopy);
      reloadData();
    } else {
      await gridRef.current.api.setColumnFilterModel("presence_status", {
        filterType: "text",
        type: "equals",
        filter: value,
      });
      gridRef.current.api.onFilterChanged();
    }
  };

  /**
   * Open selected datalogs in search page
   */
  const viewInHomepage = () => {
    const fileNames = gridSelectionData[selectionStoreKey].map((data) => {
      return data.file_name;
    });
    const requestParams = {
      template_key: "home",
      file_name: fileNames.join(","),
    };
    const queryString = Helper.createQueryString(requestParams);
    Helper.renderPage(
      `#home?${queryString}`,
      "home",
      `#home?${queryString}`,
      queryClient,
      false,
      true,
    );
  };

  /**
   * Open analysis excluding selected datalogs
   */
  const excludeDatalog = () => {
    let selectedNodes = [];
    let selectedRows = [];
    gridRef.current.api.forEachNode((node) => {
      if (!node.isSelected()) {
        selectedNodes.push(node);
        selectedRows.push(node.data);
      }
    });
    const input = Helper.formatAnalysisInput(
      selectedNodes,
      selectedRows,
      gridRef.current,
      component,
    );
    input.src_type = "dsk";
    input.src_value = input.dsk;

    if (Helper.hasValue(urlParams[pageKey].tnum)) {
      input.tnum = urlParams[pageKey].tnum;
    }
    if (Helper.hasValue(urlParams[pageKey].tnum_dsk)) {
      input.tnum_dsk = urlParams[pageKey].tnum_dsk;
    }

    Helper.generateAnalysis(
      urlParams[pageKey].template_key,
      input,
      presetAnalysisTemplates,
      queryClient,
      message,
      false,
      true,
    );
  };

  /**
   * Download table data in CSV format
   */
  const downloadTable = () => {
    const loadingStatusKey = "downloadTable";
    downloadTableAsCSV(loadingStatusKey);
  };

  /**
   * Generate MPR maps
   */
  const generateMPRMaps = () => {
    // TODO
  };

  /**
   * Update columns
   * Will only add/remove columns but will not update existing columns
   *
   * @param {array} newColumnDefs
   * @param {boolean} applyUserSettings
   */
  const updateColumns = (newColumnDefs, applyUserSettings = true) => {
    let updatedColumnDefs = [];
    const gridLastState =
      Helper.getUserSettings(
        UserSettingsKeys.grid_last_state,
        "sitewide.preferences.appearance",
        {},
      )?.value ?? true;
    if (applyUserSettings && gridLastState) {
      let gridUserSettings = Helper.getUserSettings(
        UserSettingsKeys.grid_column_state,
        "grid",
        component,
        getUserSettingsFilters(),
      );
      if (gridUserSettings && gridUserSettings.length > 0) {
        const filteredUserSettings = removeDynamicColumns(gridUserSettings);
        const missingColumns = getMissingColumns(
          filteredUserSettings,
          userColumnDefs.current,
        );
        newColumnDefs = insertMissingColumns(
          filteredUserSettings,
          missingColumns,
        );
        setHasUserSettings(true);
        setAutoSizeColumnDefs(Object.values(missingColumns));
        setMaintainColumnOrder(false);
      } else {
        setHasUserSettings(false);
        setAutoSizeColumnDefs([]);
      }
    } else {
      setHasUserSettings(false);
      setAutoSizeColumnDefs([]);
    }
    updatedColumnDefs = filterColumnsByFilterProps(
      newColumnDefs,
      userColumnDefs.current,
    );
    updatedColumnDefs = generateColumnDefinitions(
      updatedColumnDefs,
      Helper.cloneObject(userColumnDefs.current),
    );
    setColumnDefs(updatedColumnDefs);
  };

  /**
   * Remove dynamic columns from saved user grid settings
   *
   * @param {array} gridUserSettings
   * @returns {array} filteredUserSettings
   */
  const removeDynamicColumns = (gridUserSettings) => {
    const filteredUserSettings = gridUserSettings.filter((setting) => {
      return !dynamicColumnRegexes.some((regex) => regex.test(setting.field));
    });

    return filteredUserSettings;
  };

  /**
   * Get missing column definitions that is present in blueprint but not in current column definitions
   *
   * @param {array} componentColumnDefs
   * @param {array} userColumnDefs
   * @returns {object} missingColumns
   */
  const getMissingColumns = (componentColumnDefs, userColumnDefs) => {
    const key = "field";
    const fieldKeys = new Set(componentColumnDefs.map((colDef) => colDef[key]));
    let missingColumns = {};
    userColumnDefs.forEach((colDef, i) => {
      if (!fieldKeys.has(colDef[key])) {
        missingColumns[i] = colDef;
      }
    });

    return missingColumns;
  };

  /**
   * Insert missing column definitions that is present in blueprint but not in current column definitions
   *
   * @param {array} filteredUserSettings
   * @param {object} missingColumns
   * @returns {array} filteredUserSettings
   */
  const insertMissingColumns = (filteredUserSettings, missingColumns) => {
    Object.keys(missingColumns).forEach((key) => {
      filteredUserSettings.splice(key, 0, missingColumns[key]);
    });

    return filteredUserSettings;
  };

  /**
   * Go to page number specified
   *
   * @param {int} page
   */
  const goToPage = (page) => {
    gridRef.current.api.paginationGoToPage(page - 1);
  };

  /**
   * Select all rows in current page
   */
  const selectPage = useCallback(() => {
    if (isServerSide()) {
      const currentPageRows = getCurrentPageRows();
      let currentPageRowIds = currentPageRows.map((row) => {
        return row.id;
      });

      // append current page rows with selected rows and remove duplicate
      const selectionState = gridRef.current.api.getServerSideSelectionState();
      const toggledNodes = [
        ...new Set([...currentPageRowIds, ...selectionState.toggledNodes]),
      ];

      gridRef.current.api.setServerSideSelectionState({
        selectAll: false,
        toggledNodes: toggledNodes,
      });
    } else {
      const startEndRowIndex = getStartEndRowIndex();
      selectRowsByIndex(startEndRowIndex[0], startEndRowIndex[1]);
    }
  }, []);

  /**
   * Get start and end row index of current page
   *
   * @returns {array} startEndRowIndex
   */
  const getStartEndRowIndex = () => {
    const paginationSize = gridRef.current.api.paginationGetPageSize();
    const currentPage = gridRef.current.api.paginationGetCurrentPage();
    const rowCount = gridRef.current.api.getDisplayedRowCount();
    const startIndex = currentPage * paginationSize;
    let endIndex = startIndex + paginationSize - 1;
    if (endIndex > rowCount - 1) {
      endIndex = rowCount - 1;
    }

    const startEndRowIndex = [startIndex, endIndex];
    return startEndRowIndex;
  };

  /**
   * Get rows of current page
   *
   * @returns {array} currentPageRows
   */
  const getCurrentPageRows = () => {
    let currentPageRows = [];
    const startEndRowIndex = getStartEndRowIndex();

    if (isServerSide()) {
      gridRef.current.api.forEachNode((node) => {
        if (
          node.rowIndex >= startEndRowIndex[0] &&
          node.rowIndex <= startEndRowIndex[1]
        ) {
          currentPageRows.push(node);
        }
      });
    } else {
      gridRef.current.api.forEachNodeAfterFilterAndSort((node) => {
        if (
          node.rowIndex >= startEndRowIndex[0] &&
          node.rowIndex <= startEndRowIndex[1]
        ) {
          currentPageRows.push(node);
        }
      });
    }

    return currentPageRows;
  };

  /**
   * Select all rows within range of last 2 selected rows
   */
  const selectRange = () => {
    if (
      Number.isInteger(currentSelectedRowIndex) &&
      Number.isInteger(previousSelectedRowIndex)
    ) {
      const startIndex =
        currentSelectedRowIndex > previousSelectedRowIndex
          ? previousSelectedRowIndex
          : currentSelectedRowIndex;
      const endIndex =
        currentSelectedRowIndex > previousSelectedRowIndex
          ? currentSelectedRowIndex
          : previousSelectedRowIndex;
      selectRowsByIndex(startIndex, endIndex);
    } else {
      message.warning("Please select a valid range.");
    }
  };

  /**
   * Invert grid selection
   */
  const invertSelection = () => {
    if (isServerSide()) {
      const currentPageRows = getCurrentPageRows();
      currentPageRows.forEach((node) => {
        node.setSelected(!node.isSelected());
      });
    } else {
      gridRef.current.api.forEachNodeAfterFilterAndSort((node) => {
        node.setSelected(!node.isSelected());
      });
    }
  };

  /**
   * Clear all selected rows
   */
  const clearSelection = () => {
    if (isServerSide()) {
      gridRef.current.api.setServerSideSelectionState({
        selectAll: false,
        toggledNodes: [],
      });
    } else {
      gridRef.current.api.deselectAll();
    }
  };

  /**
   * View raw data of selected test
   */
  const viewTestRawData = () => {
    const selectedRows = GridHelper.getSelectedRows(
      gridSelectionData,
      selectionStoreKey,
      detailGridComponents[detailGridComponentsKey],
    );
    let invalidMessage;
    if (selectedRows.length < 1) {
      invalidMessage = "You must select a test to view raw data.";
    } else if (selectedRows.length > 1) {
      invalidMessage = "You can select up to 1 test only to view raw data.";
    }
    if (invalidMessage) {
      message.warning(invalidMessage);
    } else {
      setRawDataTestNumber(selectedRows[0].tnum);
      let hasRawDataTab = false;
      // switch to Raw Data tab and set selected test
      tabsData[pageKey].forEach((tabData) => {
        const tabItem = Helper.filterArrayAndFind(
          tabData.items,
          "key",
          "rawDataTab",
        );
        if (tabItem) {
          tabData.setActiveTabKey("rawDataTab");
          hasRawDataTab = true;
        }
      });
      if (hasRawDataTab === false) {
        // open Raw Data page and set selected test
        const analysisInput = {
          lot_id: urlParams[pageKey].lot_id.split(","),
          mfg_process: [urlParams[pageKey].mfg_process],
          src_type: urlParams[pageKey].src_type,
          src_value: urlParams[pageKey].src_value.split(","),
        };
        if (analysisInput.src_type === "dsk") {
          analysisInput.dsk = analysisInput.src_value;
        }
        Helper.openAnalysis(
          "raw_data",
          analysisInput,
          presetAnalysisTemplates,
          queryClient,
          message,
        );
      }
    }
  };

  /**
   * Filter rows by test type
   *
   * @param {string} value The value of the selected option
   */
  const filterByTestType = async (value) => {
    if (isServerSide()) {
      let gridFiltersCopy = Helper.cloneObject(gridFilters);
      gridFiltersCopy.type = value;
      setGridFilters(gridFiltersCopy);
      reloadData();
    } else {
      if (value === "all") {
        gridRef.current.api.setFilterModel(null);
      } else {
        await gridRef.current.api.setColumnFilterModel("test_type", {
          filterType: "text",
          type: "equals",
          filter: value,
        });
        gridRef.current.api.onFilterChanged();
      }
    }
  };

  /**
   * Filter rows by test types
   *
   * @param {array} values The value of the selected options
   */
  const filterByTestTypes = async (values) => {
    if (isServerSide()) {
      let gridFiltersCopy = Helper.cloneObject(gridFilters);
      gridFiltersCopy.test_type = values.join();
      setGridFilters(gridFiltersCopy);
      reloadData();
    } else {
      if (values.length === 0) {
        gridRef.current.api.setColumnFilterModel("test_type", null);
      } else {
        await gridRef.current.api.setColumnFilterModel("test_type", {
          values: values,
        });
        gridRef.current.api.onFilterChanged();
      }
    }
  };

  /**
   * Set selected part ID data to the state variable
   */
  const analyseParts = () => {
    setSelectedPartIdData(gridSelectionData[selectionStoreKey]);
  };

  /**
   * Add tests to traceability setup table
   */
  const addTestsToTraceability = () => {
    eventHandlers.addTestsToTraceability();
  };

  /**
   * Handles the adding of entire table as condition
   */
  const addSingleCondition = () => {
    eventHandlers.addSingleCondition(gridWrapperRef.current);
  };

  /**
   * Handles the adding of multiple conditions/row
   */
  const addMultiCondition = () => {
    eventHandlers.addMultiCondition(gridRef.current.api);
  };

  /**
   * Handles the opening of simulation tab
   */
  const openSimulationTab = () => {
    // scroll to test tabs area, setting slight timeout to wait for the tab content to render
    setTimeout(() => {
      npiSimulationTestTabsRef.current?.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }, 500);

    setSelectedRowsForNpiSimulation(() =>
      gridRef.current.api.getSelectedRows(),
    );
  };

  /**
   * Delete tests from traceability setup table
   */
  const deleteTestsFromTraceability = () => {
    eventHandlers.deleteTestsFromTraceability();
  };

  /**
   * Add some data of selected rows to recipe section of traceability
   */
  const addSelectedRowsToTraceabilityRecipe = () => {
    const selectedRows = gridRef.current.api.getSelectedRows();
    const newVarOptions = selectedRows.map((obj) => {
      const actualValue = obj[obj.conversion];
      return {
        value: obj.variable,
        label: `${obj.variable} (${actualValue})`,
        actualValue: actualValue,
        tnum: obj.test_number,
      };
    });
    eventHandlers.setVariablesFromTestsTable((prevState) => {
      // Merge the new and current options while updating the current variables with the new ones
      return unionBy(newVarOptions, prevState, "tnum");
    });
  };

  /**
   * Clear the traceability variables in the table
   */
  const clearTraceabilityVariables = () => {
    clearSelection();
    eventHandlers.clearTraceabilityVariables(gridRef.current);
  };

  /**
   * Edit the output value of metadata header
   */
  const editMetadataOutputValue = () => {
    eventHandlers.editMetadataOutputValue();
  };

  /**
   * Delete the output value of metadata header
   */
  const deleteMetadataOutputValue = () => {
    eventHandlers.deleteMetadataOutputValue();
  };

  /**
   * Save the metadata recipe
   */
  const saveMetadataRecipe = () => {
    eventHandlers.saveMetadataRecipe();
  };

  /**
   * Edit the output value of metadata header
   *
   * @param {string} value The selected option
   */
  const simulateMetadataRecipe = (value) => {
    eventHandlers.simulateMetadataRecipe(value);
  };

  /**
   * Select rows by start and end index
   *
   * @param {int} startIndex
   * @param {int} endIndex
   */
  const selectRowsByIndex = (startIndex, endIndex) => {
    if (isServerSide()) {
      gridRef.current.api.forEachNode(function (node) {
        if (node.rowIndex >= startIndex && node.rowIndex <= endIndex) {
          node.setSelected(true);
        }
      });
    } else {
      gridRef.current.api.forEachNodeAfterFilterAndSort(function (node) {
        if (node.rowIndex >= startIndex && node.rowIndex <= endIndex) {
          node.setSelected(true);
        }
      });
    }
  };

  /**
   * Generate toolbar options menu based on input type and position
   *
   * @param {object} toolbar
   * @param {string} position
   * @returns {array} toolbarOptions
   */
  const createToolbarOptions = (toolbar, position = "left") => {
    let toolbarOptions = [];
    if (toolbar) {
      toolbarOptions = toolbar
        .filter((toolbarOption) => {
          const option = GridToolbarFields[toolbarOption.key];
          return (
            (!option?.position && position === "left") ||
            option?.position === position
          );
        })
        .map((toolbarOption) => {
          const option = GridToolbarFields[toolbarOption.key];
          const toolbarMenuItem = getToolbarMenuItem(option, toolbarOption);
          return option?.tooltip ? (
            <Tooltip
              key={`tooltip_${toolbarOption.key}`}
              title={option.tooltip}
            >
              <div>{toolbarMenuItem}</div>
            </Tooltip>
          ) : (
            toolbarMenuItem
          );
        });
    }

    return toolbarOptions;
  };

  /**
   * Generate menu options menu based on input type
   *
   * @param {object} menu
   * @param {string} position
   * @returns {array} menuOptions
   */
  const createMenuOptions = (menu, position = "left") => {
    let menuOptions = [];
    if (menu) {
      menuOptions = menu
        .filter((menuOption) => {
          const option = GridMenuFields[menuOption.key];
          return (
            (!option.position && position === "left") ||
            option.position === position
          );
        })
        .map((menuOption) => {
          const option = GridMenuFields[menuOption.key];
          const menuMenuItem = getMenuMenuItem(option, menuOption);
          return option.tooltip ? (
            <Tooltip key={`tooltip_${menuOption.key}`} title={option.tooltip}>
              <div>{menuMenuItem}</div>
            </Tooltip>
          ) : (
            menuMenuItem
          );
        });
    }

    return menuOptions;
  };

  /**
   * Run type filter on grid when run type is available
   *
   * @param {object} values
   */
  const viewMoreRunType = (values) => {
    let gridFiltersCopy = Helper.cloneObject(gridFilters);
    if (values.run_type && values.run_type.length > 0) {
      gridFiltersCopy.run_type = values.run_type.join();
    } else {
      delete gridFiltersCopy.run_type;
    }
    setGridFilters(gridFiltersCopy);
    reloadData();
  };

  /**
   * Generate raw data table
   *
   * @param {object} values
   */
  const generateRawDataTable = (values) => {
    let gridFiltersCopy = Helper.cloneObject(gridFilters);
    if (values.tnum) {
      const testData = values.tnum.value.split("|");
      gridFiltersCopy.tNum = testData[1];
      setIsVisible(true);
    } else {
      delete gridFiltersCopy.tNum;
      setIsVisible(false);
    }
    if (values.site) {
      gridFiltersCopy.site = values.site;
    } else {
      delete gridFiltersCopy.site;
    }
    if (Array.isArray(values.pin_numbers) && values.pin_numbers.length > 0) {
      gridFiltersCopy.pin_index = values.pin_numbers.join(",");
    } else {
      delete gridFiltersCopy.pin_index;
    }
    gridFiltersCopy.per_pin = values.per_pin;
    setGridFilters(gridFiltersCopy);
    reloadData();
  };

  /**
   * Create toolbar menu label
   *
   * @param {object} labelData
   * @returns {JSX.Element}
   */
  const generateToolbarMenuLabel = (labelData) => {
    return (
      <Space align="center">
        {labelData.label}
        {labelData.tooltip && (
          <Tooltip title={labelData.tooltip}>
            <InfoCircleOutlined />
          </Tooltip>
        )}
      </Space>
    );
  };

  /**
   * Get toolbar menu item element based on input type
   *
   * @param {object} option
   * @param {object} toolbarOption
   * @returns {JSX Element} item
   */
  const getToolbarMenuItem = (option, toolbarOption) => {
    let item = null;
    let defaultValue = null;
    if (option) {
      switch (option.inputType) {
        case "dropdown":
          option.items?.forEach((item) => {
            item.disabled =
              toolbarProps[item.key] &&
              toolbarProps[item.key].disabled !== undefined
                ? toolbarProps[item.key].disabled
                : item.disabled
                  ? item.disabled
                  : false;
            item.title =
              toolbarProps[item.key] &&
              toolbarProps[item.key].tooltip !== undefined
                ? toolbarProps[item.key].tooltip
                : item.title
                  ? item.title
                  : null;
          });
          item = (
            <Dropdown
              key={`dropdown_${toolbarOption.key}`}
              menu={{
                items: option.items,
                onClick: (params) =>
                  toolbarActions[option.onClick](params, toolbarOption.key),
              }}
              trigger="click"
              disabled={
                toolbarProps[toolbarOption.key] &&
                toolbarProps[toolbarOption.key].disabled !== undefined
                  ? toolbarProps[toolbarOption.key].disabled
                  : option.disabled
                    ? option.disabled
                    : false
              }
              destroyPopupOnHide={option.destroyPopupOnHide ?? false}
            >
              <Button
                icon={option.icon ? option.icon : null}
                loading={
                  option.hasLoading ? loadingStatus[toolbarOption.key] : null
                }
              >
                <Space>
                  {toolbarProps[toolbarOption.key] &&
                  toolbarProps[toolbarOption.key].label &&
                  !toolbarProps[toolbarOption.key].disabled
                    ? generateToolbarMenuLabel(
                        toolbarProps[toolbarOption.key].label,
                      )
                    : option.label}
                  {option.hasDownArrow !== false && <DownOutlined />}
                </Space>
              </Button>
            </Dropdown>
          );
          break;
        case "select":
          defaultValue =
            option.userSettings?.key &&
            Helper.getUserSettings(option.userSettings.key)
              ? Helper.getUserSettings(option.userSettings.key)
              : (option.defaultValue ?? null);

          item = option.component ? (
            React.createElement(option.component, {
              key: `select_${toolbarOption.key}`,
              componentKey: `select_${toolbarOption.key}`,
              pageKey: pageKey,
              defaultValue: defaultValue,
              placeholder: option.placeholder,
              className: option.className,
              labelInValue: option.labelInValue,
              onChange: toolbarActions[option.onChange],
              params: option.params,
              mode: option.mode,
              gridId: gridId,
              allowClear: option.allowClear,
              disabled:
                toolbarProps[toolbarOption.key]?.disabled ??
                option.disabled ??
                false,
            })
          ) : (
            <Space key={`select_${toolbarOption.key}_wrapper`}>
              {option.label && <Text>{option.label}:</Text>}
              <Select
                key={`select_${toolbarOption.key}`}
                className={option.className ?? "w-32"}
                showSearch={option.showSearch ?? true}
                placeholder={option.placeholder}
                defaultValue={defaultValue}
                mode={option.mode ?? "single"}
                allowClear={option.allowClear ?? true}
                maxTagCount={option.maxTagCount ?? null}
                optionFilterProp="children"
                onChange={(value) => {
                  toolbarActions[option.onChange](value);
                }}
                filterOption={(input, option) =>
                  (option?.label ?? "")
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
                options={option.options}
                disabled={
                  toolbarProps[toolbarOption.key]?.disabled ??
                  option.disabled ??
                  false
                }
              />
            </Space>
          );
          if (
            option.defaultValue !== undefined &&
            option.defaultValue !== defaultValue &&
            typeof toolbarActions[option.onChange] === "function"
          ) {
            onFirstDataRenderedActions.push({
              fn: toolbarActions[option.onChange],
              params: defaultValue,
            });
          }
          break;
        case "checkbox":
          item = (
            <Checkbox
              key={`input_${toolbarOption.key}`}
              onChange={toolbarActions[option.onChange]}
              disabled={
                toolbarProps[toolbarOption.key] &&
                toolbarProps[toolbarOption.key].disabled !== undefined
                  ? toolbarProps[toolbarOption.key].disabled
                  : option.disabled
                    ? option.disabled
                    : false
              }
            >
              {option.label}
            </Checkbox>
          );
          break;
        case "search":
          item = (
            <Input
              key={`input_${toolbarOption.key}`}
              placeholder={
                component.name === ComponentNameMapper.raw_data_table
                  ? "Search Part ID"
                  : "Search table"
              }
              value={searched}
              allowClear
              onChange={handleSearch}
              suffix={<SearchOutlined />}
              className="w-40!"
            />
          );
          break;
        case "inputText":
          item = (
            <Input
              key={`input_${toolbarOption.key}`}
              id={option.id ?? ""}
              className="w-40"
              defaultValue={option.defaultValue ?? ""}
              placeholder={option.placeholder ?? ""}
            />
          );
          break;
        case "form":
          item = (
            <Form.Provider
              key={`form_${toolbarOption.key}`}
              onFormFinish={(name, { values }) => {
                if (name.indexOf("view_more_run_type_form") !== -1) {
                  viewMoreRunType(values);
                }
              }}
            >
              {React.createElement(option.component, {
                pageKey: pageKey,
                key: `form_component_${toolbarOption.key}`,
                gridRef: gridRef,
                pageFilters: filters[pageKey],
                customData: initialGridFilters,
              })}
            </Form.Provider>
          );
          break;
        default:
          item = (
            <Button
              key={`btn_${toolbarOption.key}`}
              icon={
                toolbarProps[toolbarOption.key] &&
                toolbarProps[toolbarOption.key].icon
                  ? toolbarIcons[toolbarProps[toolbarOption.key].icon]
                  : option.icon
                    ? option.icon
                    : null
              }
              loading={
                option.hasLoading ? loadingStatus[toolbarOption.key] : null
              }
              onClick={() => toolbarActions[option.onClick](option)}
              disabled={
                toolbarProps[toolbarOption.key] &&
                toolbarProps[toolbarOption.key].disabled !== undefined
                  ? toolbarProps[toolbarOption.key].disabled
                  : option.disabled
                    ? option.disabled
                    : false
              }
              type={
                toolbarProps[toolbarOption.key] &&
                toolbarProps[toolbarOption.key].type
                  ? toolbarProps[toolbarOption.key].type
                  : option.buttonType
                    ? option.buttonType
                    : "default"
              }
              className={
                toolbarProps[toolbarOption.key]?.visibility !== undefined
                  ? toolbarProps[toolbarOption.key].visibility
                  : "visible"
              }
            >
              {toolbarProps[toolbarOption.key] &&
              toolbarProps[toolbarOption.key].label &&
              !toolbarProps[toolbarOption.key].disabled
                ? generateToolbarMenuLabel(
                    toolbarProps[toolbarOption.key].label,
                  )
                : option.label}
            </Button>
          );
      }
    }

    return item;
  };

  /**
   * Get menu menu item element based on input type
   *
   * @param {object} option
   * @param {object} menuOption
   * @returns {JSX Element} item
   */
  const getMenuMenuItem = (option, menuOption) => {
    let item = <></>;
    if (option) {
      switch (option.inputType) {
        case "dropdown":
          item = (
            <Dropdown
              key={`dropdown_${menuOption.key}`}
              menu={{
                items: option.items,
                onClick: menuActions[option.onClick],
              }}
              trigger="click"
              disabled={option.disabled}
            >
              <Button
                icon={option.icon ? option.icon : null}
                loading={
                  option.hasLoading ? loadingStatus[menuOption.key] : null
                }
              >
                <Space>
                  {option.label}
                  {option.hasDownArrow !== false && <DownOutlined />}
                </Space>
              </Button>
            </Dropdown>
          );
          break;
        case "form":
          item = (
            <Form.Provider
              key={`form_${menuOption.key}`}
              onFormFinish={(name, { values }) => {
                if (name.indexOf("generate_raw_data_table_form") !== -1) {
                  generateRawDataTable(values);
                }
              }}
            >
              {React.createElement(option.component, {
                pageKey: pageKey,
                key: `form_component_${menuOption.key}`,
              })}
            </Form.Provider>
          );
          break;
        default:
          item = (
            <Button
              key={`btn_${menuOption.key}`}
              icon={
                menuProps[menuOption.key] && menuProps[menuOption.key].icon
                  ? menuIcons[menuProps[menuOption.key].icon]
                  : option.icon
                    ? option.icon
                    : null
              }
              onClick={menuActions[option.onClick]}
              disabled={
                menuProps[menuOption.key] && menuProps[menuOption.key].disabled
                  ? menuProps[menuOption.key].disabled
                  : option.disabled
                    ? option.disabled
                    : false
              }
              type={
                menuProps[menuOption.key] && menuProps[menuOption.key].type
                  ? menuProps[menuOption.key].type
                  : "default"
              }
            >
              {menuProps[menuOption.key] &&
              menuProps[menuOption.key].label &&
              !menuProps[menuOption.key].disabled
                ? menuProps[menuOption.key].label
                : option.label}
            </Button>
          );
      }
    }

    return item;
  };

  /**
   * Set validate selection button state
   *
   * @returns {object}
   */
  const setValidateSelectionBtnState = () => {
    const key = "validateSelection";
    let disabled;
    const selectedNodes = GridHelper.getSelectedNodes(
      gridRef.current,
      isServerSide(),
    );
    const selectedKeys = GridHelper.getSelectedKeys(
      selectedNodes,
      gridRef.current,
      component,
    );
    if (
      Object.keys(selectedKeys.groupKeys).length > 0 ||
      Object.keys(selectedKeys.selectedKeys).length === 0
    ) {
      disabled = true;
    } else {
      const validSelection = gridSelectionData[selectionStoreKey].filter(
        (data) => {
          // do not use strict comparison since value will become either boolean or string
          return data.tagged_invalid == 0;
        },
      );
      disabled =
        (validSelection.length !==
          gridSelectionData[selectionStoreKey].length &&
          validSelection.length > 0) ||
        gridSelectionData[selectionStoreKey].length < 1;
      setIsValidateSelection(
        validSelection.length === gridSelectionData[selectionStoreKey].length &&
          validSelection.length > 0
          ? false
          : true,
      );
    }

    return { key: key, value: disabled };
  };

  /**
   * Update validate selection button type and label
   */
  const updateValidateSelectionBtn = () => {
    const key = "validateSelection";
    let toolbarPropsCopy = Helper.cloneObject(toolbarProps);
    if (!toolbarPropsCopy[key]) {
      toolbarPropsCopy[key] = {};
    }
    // toolbarPropsCopy[key].type = isValidateSelection ? "default" : "primary";
    toolbarPropsCopy[key].label = {
      label: isValidateSelection
        ? "Validate Selection"
        : "Invalidate Selection",
    };
    toolbarPropsCopy[key].icon = isValidateSelection
      ? "CheckCircleOutlined"
      : "CloseCircleOutlined";
    setToolbarProps(toolbarPropsCopy);
  };

  /**
   * Update select options button label
   *
   * @param {array} selectedNodes
   */
  const updateSelectOptionsBtn = (selectedNodes) => {
    const key = "selectOptions";
    const selectedKeys = GridHelper.getSelectedKeys(
      selectedNodes,
      gridRef.current,
      component,
    );
    let tooltips = [];
    let selectCount = 0;
    Object.keys(selectedKeys.groupKeys).forEach((groupKey) => {
      tooltips.push(
        `${selectedKeys.groupKeys[groupKey].length} ${selectionNameMapper[groupKey] ? selectionNameMapper[groupKey] : groupKey}${selectedKeys.groupKeys[groupKey].length > 1 ? "s" : ""}`,
      );
      selectCount += selectedKeys.groupKeys[groupKey].length;
    });
    if (Object.keys(selectedKeys.selectedKeys).length > 0) {
      const selectedKey = Object.keys(selectedKeys.selectedKeys)[0];
      const selectionNameKey = Object.keys(selectedKeys.selectedKeys).join("_");
      tooltips.push(
        `${selectedKeys.selectedKeys[selectedKey].length} ${selectionNameMapper[selectionNameKey] ? selectionNameMapper[selectionNameKey] : selectedKey}${selectedKeys.selectedKeys[selectedKey].length > 1 ? "s" : ""}`,
      );
      selectCount += selectedKeys.selectedKeys[selectedKey].length;
    }

    let toolbarPropsCopy = Helper.cloneObject(toolbarProps);
    if (!toolbarPropsCopy[key]) {
      toolbarPropsCopy[key] = {};
    }
    if (selectCount > 0) {
      toolbarPropsCopy[key].label = {
        label: `${selectCount} Selected`,
        tooltip: tooltips.join(", "),
      };
    } else {
      toolbarPropsCopy[key].label = { label: GridToolbarFields[key].label };
    }

    setToolbarProps(toolbarPropsCopy);
  };

  /**
   * Update show breakdown per pin button state
   *
   * @param {string} key
   * @param {array} data
   */
  const updateShowBreakdownPerPinBtn = (key, data) => {
    let toolbarPropsCopy = Helper.cloneObject(toolbarProps);
    if (!toolbarPropsCopy[key]) {
      toolbarPropsCopy[key] = {};
    }
    toolbarPropsCopy[key].disabled = !data.some((row) => {
      return row.test_type === "m";
    });

    setToolbarProps(toolbarPropsCopy);
  };

  /**
   * Set duplicate recipe button state
   *
   * @returns {object}
   */
  const setDuplicateRecipeBtnState = () => {
    const key = "duplicateRecipe";
    const disabled = gridSelectionData[selectionStoreKey].length !== 1;
    return { key: key, value: disabled };
  };

  /**
   * Enable/disable the "Append Selected to Recipe" button in Traceability table
   *
   * @returns {object}
   */
  const setAppendSelectedTraceabilityTestsBtnState = () => {
    const key = "addTraceabilityVariables";
    const disabled = gridSelectionData[selectionStoreKey].length < 1;
    return { key: key, value: disabled };
  };

  /**
   * Enable/disable the "Remove Tests" button in Traceability table
   *
   * @returns {object}
   */
  const setDeleteTraceabilityTestsBtnState = () => {
    const key = "deleteTestsFromTraceability";
    const disabled = gridSelectionData[selectionStoreKey].length < 1;
    return { key: key, value: disabled };
  };

  /**
   * Enable/disable the "Add Tests" button in calculated test table
   *
   * @returns {object}
   */
  const setAddTestsBtnState = () => {
    return {
      key: "addTests",
      value: !hasLoaded,
    };
  };

  /**
   * Enable/disable the "Remove Tests" button in calculated test table
   *
   * @returns {object}
   */
  const setRemoveTestBtnState = () => {
    return {
      key: "removeTest",
      value: gridSelectionData[selectionStoreKey].length < 1,
    };
  };

  /**
   * Enable/disable the "Clear All Variables" button in calculated test table
   *
   * @returns {object}
   */
  const setClearAllVariablesBtnState = () => {
    return {
      key: "clearAllVariables",
      value: gridSelectionData[selectionStoreKey].length < 1,
    };
  };

  /**
   * Set edit recipe button state
   *
   * @returns {object}
   */
  const setEditRecipeBtnState = () => {
    const key = "editRecipe";
    const disabled = gridSelectionData[selectionStoreKey].length !== 1;
    return { key: key, value: disabled };
  };

  /**
   * Set the state of the Edit Ouput Value button of Metadata table
   *
   * @returns {object}
   */
  const setEditMetadataOutputBtnState = () => {
    const key = "editMetadataOutputValue";
    const disabled = gridSelectionData[selectionStoreKey].length === 0;
    return { key: key, value: disabled };
  };

  /**
   * Set the state of the Delete Ouput Value button of Metadata table
   *
   * @returns {object}
   */
  const setDeleteMetadataOutputBtnState = () => {
    const key = "deleteMetadataOutputValue";
    const disabled = gridSelectionData[selectionStoreKey].length === 0;
    return { key: key, value: disabled };
  };

  /**
   * Set delete recipe button state
   *
   * @returns {object}
   */
  const setDeleteRecipeBtnState = () => {
    const key = "deleteRecipe";
    const disabled = gridSelectionData[selectionStoreKey].length < 1;
    return { key: key, value: disabled };
  };

  /**
   * Set save recipe button state
   *
   * @returns {object}
   */
  const setSaveRecipeBtnState = () => {
    const key = "saveRecipe";
    const disabled = gridSelectionData[selectionStoreKey].length !== 1;
    return { key: key, value: disabled };
  };

  /**
   * Set view in homepage button state
   *
   * @returns {object}
   */
  const setViewInHomepageBtnState = () => {
    const key = "viewInHomepage";
    const disabled = gridSelectionData[selectionStoreKey].length < 1;
    return { key: key, value: disabled };
  };

  /**
   * Set exclude datalog button state
   *
   * @returns {object}
   */
  const setExcludeDatalogBtnState = () => {
    const key = "excludeDatalog";
    const rows = GridHelper.getAllRows(gridRef.current);
    const disabled =
      gridSelectionData[selectionStoreKey].length < 1 ||
      gridSelectionData[selectionStoreKey].length === rows.length;
    return { key: key, value: disabled };
  };

  /**
   * Set view test raw data dropdown item state
   *
   * @returns {object} state
   */
  const setViewTestRawDataDropdownItemState = () => {
    const key = "view_test_raw_data";
    const disabled =
      GridHelper.getSelectedRows(
        gridSelectionData,
        selectionStoreKey,
        detailGridComponents[detailGridComponentsKey],
      ).length !== 1;
    const state = { key: key, value: disabled };

    if (disabled) {
      state.tooltip =
        gridSelectionData[selectionStoreKey].length < 1
          ? "You must select a test to view raw data."
          : "You can select up to 1 test only to view raw data.";
    } else {
      state.tooltip = null;
    }

    return state;
  };

  /**
   * Download selected datalogs
   *
   * @param {string} loadingStatusKey
   */
  const downloadDatalogs = ({ loadingStatusKey }) => {
    const fileNames = gridSelectionData[selectionStoreKey].map(
      (obj) => obj.file_name,
    );
    const dsk = gridSelectionData[selectionStoreKey]
      .map((obj) => obj.data_struc_key)
      .join();

    if (dsk) {
      Helper.updateLoadingStatus(
        loadingStatus,
        setLoadingStatus,
        loadingStatusKey,
        true,
      );
      Helper.downloadDatalog(
        dsk,
        "raw",
        fileNames.length > 1 ? `datalog-archive-${+new Date()}` : fileNames[0],
        loadingStatus,
        setLoadingStatus,
        loadingStatusKey,
      );
    } else {
      message.warning("No datalog selected", 5);
    }
  };

  /**
   * Set delete datalog button state
   *
   * @returns {object}
   */
  const setDeleteDatalogBtnState = () => {
    const isEveryStatusValidForDeletion = gridSelectionData[
      selectionStoreKey
    ].every((item) =>
      datalogDeletionAllowedStatuses.includes(item.main_operation),
    );
    const key = "deleteDatalog";
    const disabled =
      gridSelectionData[selectionStoreKey].length < 1 ||
      !isEveryStatusValidForDeletion;
    return { key: key, value: disabled };
  };

  /**
   * Set edit datalog button state
   *
   * @returns {object}
   */
  const setEditDatalogBtnState = () => {
    const key = "editDatalog";
    const disabled = gridSelectionData[selectionStoreKey].length < 1;
    return { key: key, value: disabled };
  };

  /**
   * Set reprocess datalog button state
   *
   * @returns {object}
   */
  const setReprocessDatalogBtnState = () => {
    const key = "reprocessDatalog";
    const disabled =
      gridSelectionData[selectionStoreKey].length < 1 ||
      gridSelectionData[selectionStoreKey].some(
        (data) =>
          data["upload_type"] === "Personal" &&
          data["user_name"] !== userData.name,
      );
    const visibility = userData?.is_staff ? "visible" : "hidden";
    return { key: key, value: disabled, visibility: visibility };
  };

  /**
   * Set save table data button state
   *
   * @returns {object}
   */
  const setSaveTableDataBtnState = () => {
    const key = "save_table_data";
    const disabled = !isEditMode;
    return { key: key, value: disabled };
  };

  /**
   * Set cancel edit table data button state
   *
   * @returns {object}
   */
  const setCancelEditTableDataBtnState = () => {
    const key = "cancel_edit_table";
    const disabled = !isEditMode;
    return { key: key, value: disabled };
  };

  /**
   * Set percent drift option state
   *
   * @returns {object}
   */
  const setPercentDriftOptionState = () => {
    const key = "selectPercentDrift";
    const disabled = prerenderData.is_published === 1;
    return { key: key, value: disabled };
  };

  /**
   * Set download table button state
   *
   * @param {boolean} disabled
   */
  const setDownloadTableBtnState = (disabled) => {
    const key = "downloadTable";
    let toolbarPropsCopy = Helper.cloneObject(toolbarProps);
    toolbarPropsCopy[key] ??= {};
    toolbarPropsCopy[key].disabled = disabled;
    setToolbarProps(toolbarPropsCopy);
  };

  /**
   * Filter table by file type
   *
   * @param {string} value
   */
  const filterByFileType = async (value) => {
    if (isServerSide()) {
      let gridFiltersCopy = Helper.cloneObject(gridFilters);
      gridFiltersCopy.file_type = value;
      setGridFilters(gridFiltersCopy);
      reloadData();
    }
  };

  /**
   * Filter table by dice type
   *
   * @param {string} key
   */
  const filterByDiceType = ({ key }) => {
    let gridFiltersCopy = Helper.cloneObject(gridFilters);
    gridFiltersCopy.show_all_dice = key === "show_all_dice";
    setGridFilters(gridFiltersCopy);
    reloadData();
  };

  /**
   * Reprocess selected datalog(s)
   *
   */
  const reprocessDatalog = () => {
    const fileNames = gridSelectionData[selectionStoreKey].map(
      (data) => data["file_name"],
    );
    const allProcessedOrErrorStatus = gridSelectionData[
      selectionStoreKey
    ].every(
      (data) =>
        data.main_operation === "Processed" || data.main_operation === "Error",
    );

    if (allProcessedOrErrorStatus) {
      confirm({
        title: "Confirm Reprocess",
        width: "30%",
        centered: true,
        content: (
          <>
            <Paragraph>
              <Text>Files for reprocessing:</Text>
              {fileNames.map((fileName, index) => (
                <span key={index} className="break-all block">
                  {index + 1}. {fileName}
                </span>
              ))}
            </Paragraph>
            <Paragraph>
              <Text>Do you want to continue?</Text>
            </Paragraph>
          </>
        ),
        onOk() {
          Api.reprocessDatalog(
            (res) => {
              if (res.success) {
                warning({
                  title: "Reprocessing...",
                  content: "Datalogs are queued for reprocessing.",
                });
                clearSelection();
              } else {
                message.warning(res.message, 10);
              }
            },
            (err) => {
              message.error(err, 10);
            },
            {
              file_name: fileNames.join(),
            },
          );
        },
      });
    } else {
      error({
        centered: true,
        title: "Incorrect File Status",
        content: "Please select datalogs with Processed or Error status only..",
      });
    }
  };

  /**
   * Edit table data
   */
  const editTableData = () => {
    setIsEditMode(true);
  };

  /**
   * Save table data
   */
  const saveTableData = () => {
    // Api.setTableData(
    //   params.url_save_endpoint,
    //   (res) => {
    //     if (res.success) {
    //       // Update edited cell original value during saving to match the server side value
    //       setUpdateEditedCellOriginalValue(true);
    //     } else {
    //       revertEditedToOriginalValue();
    //       notification.warning({
    //         message: "Save Table Data",
    //         description: res.message,
    //       });
    //     }
    //   },
    //   (err) => {
    //     revertEditedToOriginalValue();
    //     notification.error({
    //       message: "Save Table Data",
    //       description: err,
    //     });
    //   },
    //   Object.entries(editedData).map(([key, value]) => ({
    //     data_struc_key: key,
    //     ...value,
    //   })),
    // );
    setEditedData({});
    setIsEditMode(false);
  };

  /**
   * Cancel edit table data
   */
  const cancelEditTableData = () => {
    setEditedData({});
    setIsEditMode(false);
    revertEditedToOriginalValue();
  };

  /**
   * Revert edited cell value to its original data value
   */
  const revertEditedToOriginalValue = () => {
    const selectedNodes = GridHelper.getServerSideSelectedNodes(
      gridRef.current,
    );
    selectedNodes.forEach((node) => {
      userColumnDefs.current.forEach(({ field, type }) => {
        if (type === "editableColumn" && node[`${field}_original_value`]) {
          node.updateData({
            ...node.data,
            [field]: node[`${field}_original_value`],
          });
        }
      });
    });
  };

  /**
   * Synchronizes boxplot data with current page data when the page changes.
   * This is a silent no-op if it fails.
   */
  const updateDiceAnalysisBoxplot = async () => {
    try {
      await diceAnalysisUpdateBoxplot({
        gridRef,
        pageKey,
        chartKeys,
        chartComponentRefs,
        prerenderData,
        // Rely on the grid's row data for now instead of doing a separate query just to prefetch chart data that matches the selected page size
        isServerSide: () => false,
        getCurrentPageRows,
        setPendingLocalChartData,
        lastChartSliceRef,
        lastChartUpdateAtRef,
        chartPageFetchInFlightRef,
        Helper,
        Api,
        params,
        filters,
        gridRequestParams,
        component,
        gridFilters,
        reloadGridFilters,
      });
    } catch (e) {
      // Silent safeguard to avoid interrupting grid pagination
      console.warn("onPaginationChanged (boxplot sync) error:", e);
    }
  };

  /**
   * Action mapper for grid toolbar
   */
  const toolbarActions = {
    notifyForDevelopment,
    validateSelection,
    toggleShowInvalid,
    onMenuItemClick,
    reloadData,
    resetTable,
    adjustColumnsEvenly,
    showFilterTableOptions,
    showFollowedTestsOnly,
    showBreakdownPerPin,
    filterTableStatus,
    filterTableSubcon,
    filterByRecipeType,
    filterByRecipeOwner,
    filterByAutoTriggerStatus,
    duplicateSelectedRecipe,
    deleteSelectedRow,
    editSelectedRecipe,
    saveSelectedRecipe,
    filterByTestType,
    filterByTestTypes,
    addTestsToTraceability,
    addSingleCondition,
    addMultiCondition,
    openSimulationTab,
    deleteTestsFromTraceability,
    addSelectedRowsToTraceabilityRecipe,
    clearTraceabilityVariables,
    editMetadataOutputValue,
    deleteMetadataOutputValue,
    simulateMetadataRecipe,
    saveMetadataRecipe,
    showAddNewUserForm,
    filterByUserType,
    filterByOnlineStatus,
    viewInHomepage,
    excludeDatalog,
    downloadTable,
    downloadDatalogs,
    filterByFileType,
    reprocessDatalog,
    filterByDiceType,
    showAddNewTeamForm,
    generateMPRMaps,
    showTestsFilterForm,
    filterByPercentDrift,
    showTestTableFilterForm,
    showAddTestsForm,
    removeTest,
    clearAllVariables,
    analyseParts,
  };

  /**
   * Icon mapper for grid toolbar
   */
  const toolbarIcons = {
    EyeOutlined: <EyeOutlined />,
    EyeInvisibleOutlined: <EyeInvisibleOutlined />,
    CheckCircleOutlined: <CheckCircleOutlined />,
    CloseCircleOutlined: <CloseCircleOutlined />,
  };

  /**
   * Action mapper for grid menu
   */
  const menuActions = {
    toggleShowWaferIdBreakdownTable,
  };

  /**
   * Icon mapper for grid menu
   */
  const menuIcons = {
    EyeOutlined: <EyeOutlined />,
    EyeInvisibleOutlined: <EyeInvisibleOutlined />,
  };

  /**
   * Action mapper for grid events
   */
  const eventActions = {
    sortYieldTrendChart,
    updateConditionValue,
    updateNpiTestSummaryTableColumnValue,
    updateDiceAnalysisBoxplot,
  };

  /**
   * Actions to be executed the first time data is rendered into the grid
   */
  const onFirstDataRenderedActions = [];

  /**
   * Get column state
   *
   * @returns {object} columnState
   */
  const getColumnState = () => {
    let columnState = gridRef.current.api.getColumnState();
    const gridColumnDefs = gridRef.current.api.getColumnDefs();
    columnState = columnState.filter(
      (state) => state.colId !== "ag-Grid-AutoColumn",
    );
    columnState.forEach(function (state, i) {
      gridColumnDefs.forEach(function (def) {
        if (def.colId === state.colId) {
          const extraFields = Object.keys(def).filter(
            (x) => !Object.keys(state).includes(x),
          );
          extraFields.forEach(function (field) {
            columnState[i][field] = def[field];
          });
        }
      });
    });
    columnState = formatColumnStateForSaving(columnState);

    return columnState;
  };

  /**
   * Format column state for saving
   * Replace function value with function name
   *
   * @param {object} columnState - column state to be saved
   * @returns {object} columnState - formatted column state data
   */
  const formatColumnStateForSaving = (columnState) => {
    Object.keys(columnState).forEach(function (key) {
      const def = columnState[key];
      if (typeof def.valueGetter === "function") {
        columnState[key].valueGetter = def.valueGetter.name;
      }
      if (typeof def.valueFormatter === "function") {
        columnState[key].valueFormatter = def.valueFormatter.name;
      }
      if (typeof def.comparator === "function") {
        columnState[key].comparator = def.comparator.name;
      }
    });

    return columnState;
  };

  /**
   * Apply column state
   *
   * @param {object} columnState
   */
  const applyColumnState = (columnState) => {
    setMaintainColumnOrder(false);
    const gridColumnDefs = generateColumnDefinitions(
      columnState,
      Helper.cloneObject(userColumnDefs.current),
    );
    gridRef.current.api.setGridOption("columnDefs", gridColumnDefs);
  };

  /**
   * Show confirmation to delete selected table settings
   */
  const showDeleteSelectedTableSettingsConfirm = () => {
    confirm({
      title: "Confirm Deletion",
      icon: <ExclamationCircleOutlined />,
      content: (
        <>
          <Paragraph>
            Are you sure you want to delete selected table settings permanently?
          </Paragraph>
          <Paragraph>
            <Text strong>Warning!</Text>{" "}
            <Text>This action cannot be undone!</Text>
          </Paragraph>
        </>
      ),
      okText: "Delete",
      cancelText: "Cancel",
      onOk() {
        deleteSelectedTableSettings();
      },
    });
  };

  /**
   * Delete selected table settings
   */
  const deleteSelectedTableSettings = () => {
    Api.deleteTableSettings(
      (res) => {
        if (res.success) {
          message.success(res.message);
          loadTableSettingsForm.resetFields();
          setIsDisableDeleteTableSettingsButton(true);
          setIsDisableLoadTableSettingsButton(true);
          setShouldUpdateSavedTableOptions(!shouldUpdateSavedTableOptions);
        } else {
          message.warning(res.message);
        }
      },
      (err) => {
        message.error(err);
      },
      {
        id: loadTableSettingsForm.getFieldValue("table_settings_key"),
      },
    );
  };

  /**
   * Load selected table settings
   */
  const loadSelectedTableSettings = () => {
    Api.getTableSettings(
      (res) => {
        if (res.success) {
          setIsDisableDeleteTableSettingsButton(true);
          setIsDisableLoadTableSettingsButton(true);
          setIsLoadTableSettingsModalOpen(false);
          applyColumnState(JSON.parse(res.data.table_props));
          loadTableSettingsForm.resetFields();
          message.success("Table settings successfully loaded");
        } else {
          message.warning(res.message);
        }
      },
      (err) => {
        message.error(err);
      },
      {
        id: loadTableSettingsForm.getFieldValue("table_settings_key"),
      },
    );
  };

  /**
   * Show confirmation to reset columns
   */
  const showResetColumnsConfirm = () => {
    confirm({
      title: "Confirm Reset Columns",
      icon: <ExclamationCircleOutlined />,
      content: (
        <Paragraph>Are you sure you want to reset table columns?</Paragraph>
      ),
      okText: "Reset",
      cancelText: "Cancel",
      onOk() {
        setMaintainColumnOrder(false);
        setShouldResetColumns(true);
      },
    });
  };

  /**
   * Show confirmation to reset table
   */
  const showResetTableConfirm = () => {
    confirm({
      title: "Confirm Table Reset",
      icon: <ExclamationCircleOutlined />,
      content: (
        <Paragraph>
          Resetting the table will clear all table filters and sorting. Are you
          sure you want to reset the table?
        </Paragraph>
      ),
      okText: "Reset",
      cancelText: "Cancel",
      onOk() {
        filterTableForm.resetFields();
        setGridFilters({});
        resetTable();
      },
    });
  };

  /**
   * Called when the aggrid model is updated
   *
   * @param {object} event
   */
  const onModelUpdated = (event) => {
    updateNoRowsOverlayDisplay(event);
    if (typeof eventHandlers.onModelUpdated === "function") {
      eventHandlers.onModelUpdated(event);
    }
  };

  /**
   * Fired when pagination page size changes
   */
  const onPaginationChanged = async () => {
    if (settings.on_pagination_changed) {
      eventActions[settings.on_pagination_changed]();
    }
  };

  /**
   * Reset grid columns
   */
  const resetColumns = () => {
    setMaintainColumnOrder(false);
    userColumnDefs.current = Helper.cloneObject(settings.column_defs);
    updateColumns(settings.column_defs, false);
    autoSizeColumns(settings.column_defs);
    message.info("Columns has been reset.");
    setShouldResetColumns(false);
  };

  /**
   * Triggered when closing add new user form modal
   */
  const closeAddNewUserForm = () => {
    if (addNewUserForm.isFieldsTouched()) {
      confirm({
        title: "Leave Without Saving?",
        icon: <ExclamationCircleOutlined />,
        content:
          "Are you sure you want to leave this page without saving? All unsaved changes will be lost.",
        okText: "Yes",
        cancelText: "No",
        onOk() {
          setIsAddNewUserModalOpen(false);
        },
      });
    } else {
      setIsAddNewUserModalOpen(false);
    }
  };

  /**
   * Render corresponding filter form according to component name
   *
   * @param {string} componentName
   * @returns {JSX.Element}
   */
  const renderFilterForm = (componentName) => {
    let element = <></>;
    switch (componentName) {
      case ComponentNameMapper.engineering_upload:
        element = (
          <EngineeringTableFiltersForm
            form={filterTableForm}
            gridId={gridId}
            gridFilters={gridFilters}
            setGridFilters={setGridFilters}
            setIsFilterTableModalOpen={setIsFilterTableModalOpen}
          />
        );
    }
    return element;
  };

  /**
   * Get paging panel containing grid pagination elements
   *
   * @param {string} position
   * @returns {JSX.Element}
   */
  const getPagingPanel = (position = "top") => {
    return (
      <div
        className={`ag-theme-balham text-right ${position === "bottom" && "mt-2"}`}
      >
        <Space>
          <div ref={gridPagingPanel} className="paging-panel"></div>
          <div>
            <Select
              defaultValue={pageSize}
              onChange={setPageSize}
              options={pageSizeOptions}
              popupMatchSelectWidth={false}
            />
          </div>
          <Space>
            <Text>Go to</Text>
            <InputNumber className="w-16" min={1} onChange={goToPage} />
          </Space>
        </Space>
      </div>
    );
  };

  /**
   * Set background color of row if highlight data is set
   *
   * @param {object} params
   * @returns {object}
   */
  const getRowStyle = (params) => {
    if (params.node.data?.highlight) {
      return { background: params.node.data.highlight };
    }
  };

  return (
    <div
      ref={gridWrapperRef}
      className={`${wrapperClassName} ${settings.fill_height ? "flex grow flex-col h-full" : ""}`}
    >
      {contextHolder}
      {component.props.menu && (
        <div className="ag-toolbar ag-theme-balham flex mb-4">
          <Space>{createMenuOptions(component.props.menu)}</Space>
          <Space className="m-auto">
            {createMenuOptions(component.props.menu, "center")}
          </Space>
        </div>
      )}
      {settings.show_no_data && !isVisible && (
        <Empty
          className="mt-24"
          description="Please select a test number or test name to view the raw data."
        />
      )}
      {isVisible && (
        <>
          <div>
            {gridTitle && settings.show_title !== false && (
              <Title level={5}>{gridTitle}</Title>
            )}
          </div>
          <div className="ag-toolbar ag-theme-balham flex justify-between">
            {isGridReady && (
              <>
                <Flex wrap gap="small" align="center">
                  {createToolbarOptions(component.props.toolbar)}
                </Flex>
                <div className="flex gap-[4px] items-center ml-auto">
                  {createToolbarOptions(component.props.toolbar, "right")}
                </div>
              </>
            )}
            {pagination !== false &&
              settings.pagination_position !== "bottom" &&
              getPagingPanel(settings.pagination_position)}
          </div>
          <div
            className="ag-theme-balham mt-3"
            style={{
              ...settings,
              height: gridHeight,
            }}
          >
            <AgGridReact
              theme={"legacy"}
              ref={gridRef}
              rowModelType={rowModelType}
              suppressClickEdit={suppressClickEdit}
              stopEditingWhenCellsLoseFocus={stopEditingWhenCellsLoseFocus}
              columnDefs={columnDefs}
              defaultColDef={defaultColDef}
              defaultCsvExportParams={{ fileName: component.name ?? "export" }}
              defaultExcelExportParams={{
                fileName: component.name ?? "export",
              }}
              columnTypes={columnTypes}
              rowClass={
                settings.row_class !== undefined ? settings.row_class : null
              }
              rowClassRules={rowClassRules}
              paginationPageSize={pageSize}
              animateRows={true}
              accentedSort={true}
              suppressAggFuncInHeader={true}
              suppressColumnVirtualisation={true}
              masterDetail={masterDetail}
              isRowMaster={isRowMaster}
              keepDetailRows={true}
              detailRowAutoHeight={true}
              maintainColumnOrder={false}
              pagination={pagination}
              paginationPageSizeSelector={false}
              rowSelection={{
                mode:
                  settings.rowSelection === "single" ? "singleRow" : "multiRow",
                enableClickSelection: !suppressRowClickSelection,
                checkboxes:
                  component?.props.settings.checkbox_selection ?? false,
                headerCheckbox:
                  component?.props.settings.header_checkbox_selection ?? false,
                enableSelectionWithoutKeys: true,
              }}
              suppressRowVirtualisation={suppressRowVirtualisation}
              domLayout={settings.height === "auto" ? "autoHeight" : "normal"}
              sideBar={sideBar}
              components={components}
              columnMenu="legacy"
              onGridReady={onGridReady}
              onFirstDataRendered={onFirstDataRendered}
              onSelectionChanged={onSelectionChanged}
              onSortChanged={onSortChanged}
              onFilterChanged={onFilterChanged}
              onRowSelected={onRowSelected}
              onCellEditingStopped={onCellEditingStopped}
              onCellEditingStarted={onCellEditingStarted}
              onCellClicked={onCellClicked}
              onColumnVisible={onColumnVisible}
              onColumnMoved={onColumnMoved}
              onColumnResized={onColumnResized}
              onColumnPinned={onColumnPinned}
              onDisplayedColumnsChanged={onDisplayedColumnsChanged}
              onModelUpdated={onModelUpdated}
              onPaginationChanged={onPaginationChanged}
              noRowsOverlayComponent={NoRowsOverlayComponent}
              getRowStyle={getRowStyle}
              {...additionalGridOptions}
            />
          </div>
          {pagination !== false &&
            settings.pagination_position === "bottom" &&
            getPagingPanel(settings.pagination_position)}
          {isLoadingData && (
            <Alert
              className="!mt-2 !bg-white"
              message={
                <Flex align="center" gap="small">
                  <Image
                    src="/images/loading-spinner.gif"
                    alt="Loading..."
                    height={20}
                    preview={false}
                  />
                  <span>
                    {" "}
                    Please wait... still processing some data{" "}
                    <strong>
                      ({loadingData.count} of {loadingData.total} -{" "}
                      {loadingData.pct}%)
                    </strong>
                    . You can continue using the table.
                  </span>
                </Flex>
              }
              type="info"
            />
          )}
          {isLoadingDataDone && (
            <Alert className="!mt-2" message="Done" type="success" showIcon />
          )}
        </>
      )}
      {/* Label Menu */}
      {component.props.toolbar &&
        component.props.toolbar.some((menu) => menu.key === "label") && (
          <>
            <Modal
              title="Add Label"
              open={isAddLabelModalOpen}
              okText="Apply to the Selected"
              cancelText="Close"
              onCancel={() => setIsAddLabelModalOpen(false)}
            >
              <LabelSelectionForm hasPublicLabelFilter={false} />
            </Modal>
            <Modal
              title="Filter Table By Label"
              open={isFilterByLabelModalOpen}
              okText="View Selected Label"
              cancelText="Close"
              onCancel={() => setIsFilterByLabelModalOpen(false)}
            >
              <LabelSelectionForm />
            </Modal>
            <Modal
              title="Create New Label"
              open={isCreateNewLabelModalOpen}
              onCancel={() => setIsCreateNewLabelModalOpen(false)}
              footer={[
                <Button
                  key="create_label_close_btn"
                  onClick={() => setIsCreateNewLabelModalOpen(false)}
                >
                  Close
                </Button>,
                <Button key="save_new_label" type="primary">
                  Save Label
                </Button>,
                <Button key="apply_label" type="primary">
                  Apply to the Selected
                </Button>,
              ]}
            >
              <CreateNewLabelForm />
            </Modal>
          </>
        )}
      {/* Manage Table Menu */}
      {component.props.toolbar &&
        component.props.toolbar.some((menu) => menu.key === "manageTable") && (
          <>
            <Modal
              title="Add Column"
              open={isAddNewColumnModalOpen}
              okText="Add to Table"
              cancelText="Cancel"
              onCancel={() => setIsAddNewColumnModalOpen(false)}
            >
              <AddNewColumnForm />
            </Modal>
            <Modal
              title="Save Table Settings"
              open={isSaveTableSettingsModalOpen}
              okText="Save"
              cancelText="Close"
              onOk={() => saveTableSettingsForm.submit()}
              onCancel={() => setIsSaveTableSettingsModalOpen(false)}
              okButtonProps={{
                disabled: isDisableSaveTableSettingsButton,
              }}
            >
              <SaveTableSettingsForm
                saveTableSettingsForm={saveTableSettingsForm}
                gridId={gridId}
                shouldUpdateSavedTableOptions={shouldUpdateSavedTableOptions}
                setIsSaveTableSettingsModalOpen={
                  setIsSaveTableSettingsModalOpen
                }
                setIsDisableSaveTableSettingsButton={
                  setIsDisableSaveTableSettingsButton
                }
                setShouldUpdateSavedTableOptions={
                  setShouldUpdateSavedTableOptions
                }
                getColumnState={getColumnState}
              />
            </Modal>
            <Modal
              title="Load Saved Columns"
              open={isLoadTableSettingsModalOpen}
              onCancel={() => setIsLoadTableSettingsModalOpen(false)}
              footer={[
                <Button
                  key="close_btn"
                  onClick={() => setIsLoadTableSettingsModalOpen(false)}
                >
                  Close
                </Button>,
                <Button
                  key="delete_btn"
                  danger
                  onClick={showDeleteSelectedTableSettingsConfirm}
                  disabled={isDisableDeleteTableSettingsButton}
                >
                  Delete
                </Button>,
                <Button
                  key="load_btn"
                  type="primary"
                  onClick={loadSelectedTableSettings}
                  disabled={isDisableLoadTableSettingsButton}
                >
                  Load Selected Table
                </Button>,
              ]}
            >
              <LoadTableSettingsForm
                loadTableSettingsForm={loadTableSettingsForm}
                gridId={gridId}
                shouldUpdateSavedTableOptions={shouldUpdateSavedTableOptions}
                setIsDisableDeleteTableSettingsButton={
                  setIsDisableDeleteTableSettingsButton
                }
                setIsDisableLoadTableSettingsButton={
                  setIsDisableLoadTableSettingsButton
                }
              />
            </Modal>
            <Modal
              title="Add Parametric Column"
              open={isAddParametricColumnModalOpen}
              okText="Add to Table"
              cancelText="Cancel"
              onCancel={() => setIsAddParametricColumnModalOpen(false)}
            >
              <AddParametricColumnForm />
            </Modal>
            <Modal
              className="top-5"
              title="Add Bin Column"
              width="35%"
              open={isAddBinColumnModalOpen}
              okText="Add to Table"
              cancelText="Cancel"
              onCancel={() => setIsAddBinColumnModalOpen(false)}
            >
              <AddBinColumnForm />
            </Modal>
          </>
        )}
      {/* Filter Table */}
      {component.props.toolbar &&
        component.props.toolbar.some((menu) => menu.key === "filterTable") && (
          <>
            <Modal
              title="Filter Table"
              open={isFilterTableModalOpen}
              okText="Apply Filter"
              cancelText="Close"
              onOk={() => filterTableForm.submit()}
              onCancel={() => setIsFilterTableModalOpen(false)}
            >
              {renderFilterForm(component.name)}
            </Modal>
          </>
        )}
      {/* Add New Team */}
      {component.props.toolbar &&
        component.props.toolbar.some((menu) => menu.key === "addNewTeam") && (
          <Modal
            destroyOnClose={true}
            title="Add New Team"
            open={isAddNewTeamModalOpen}
            okText="Add"
            cancelText="Close"
            onOk={() => {
              addNewTeamForm.submit();
            }}
            okButtonProps={{
              disabled: !isAddNewTeamFormValuesValid,
            }}
            onCancel={() => {
              setIsAddNewTeamModalOpen(false);
              setIsAddNewTeamFormValuesValid(false);
              addNewTeamForm.resetFields();
            }}
          >
            <AddNewTeamForm
              form={addNewTeamForm}
              setIsAddNewTeamModalOpen={setIsAddNewTeamModalOpen}
              setIsAddNewTeamFormValuesValid={setIsAddNewTeamFormValuesValid}
              reloadTeamsData={reloadData}
            />
          </Modal>
        )}

      {/* Test Table Filter */}
      {component.props.toolbar &&
        component.props.toolbar.some(
          (menu) => menu.key === "testTableFilter",
        ) && (
          <TestTableFilterFormModal
            isTestTableFilterModalOpen={isTestTableFilterModalOpen}
            setIsTestTableFilterModalOpen={setIsTestTableFilterModalOpen}
            testTableFilterForm={testTableFilterForm}
            gridFilters={gridFilters}
            setGridFilters={setGridFilters}
            reloadData={reloadData}
          />
        )}

      {/* Add Tests to Calc Test */}
      {component.props.toolbar &&
        component.props.toolbar.some((menu) => menu.key === "addTests") && (
          <AddTestsFormModal
            isAddTestsModalOpen={isAddTestsModalOpen}
            setIsAddTestsModalOpen={setIsAddTestsModalOpen}
            addTestsForm={addTestsForm}
            gridRef={gridRef}
            pageKey={pageKey}
            filters={filters}
            addUpdateTableRow={addUpdateTableRow}
            tableUniqueCols={component?.table_unique_cols}
          />
        )}

      {component.props.toolbar &&
        component.props.toolbar.some((menu) => menu.key === "testsFilter") && (
          <Modal
            width="40%"
            open={isTestsFilterModalOpen}
            onCancel={() => setIsTestsFilterModalOpen(false)}
            footer={[
              <Button
                key="cancel"
                onClick={() => setIsTestsFilterModalOpen(false)}
              >
                Cancel
              </Button>,
              <Button
                key="clear_all"
                onClick={() => {
                  testsFilterForm.resetFields();
                }}
              >
                Clear All
              </Button>,
              <Button
                key="apply"
                type="primary"
                onClick={() => {
                  testsFilterForm.submit();
                  setIsTestsFilterModalOpen(false);
                }}
              >
                Apply
              </Button>,
            ]}
            centered
            closable
            destroyOnClose={true}
          >
            <TestsFilterForm
              testsFilterForm={testsFilterForm}
              gridFilters={gridFilters}
              setGridFilters={setGridFilters}
              reloadTestsData={reloadData}
            />
          </Modal>
        )}
      {/* Duplicate Recipe */}
      {component.props.toolbar &&
        component.props.toolbar.some(
          (menu) => menu.key === "duplicateRecipe",
        ) && (
          <>
            <Modal
              title="Duplicate Recipe"
              open={isDuplicateRecipeModalOpen}
              okText="Save"
              cancelText="Close"
              onOk={() => duplicateRecipeForm.submit()}
              onCancel={() => setIsDuplicateRecipeModalOpen(false)}
              confirmLoading={loadingStatus["duplicate_recipe"]}
            >
              <DuplicateRecipeForm
                form={duplicateRecipeForm}
                recipeData={selectedRecipeData}
                setIsDuplicateRecipeModalOpen={setIsDuplicateRecipeModalOpen}
                loadingStatus={loadingStatus}
                setLoadingStatus={setLoadingStatus}
                loadingStatusKey="duplicate_recipe"
                reloadRecipesData={reloadData}
              />
            </Modal>
          </>
        )}
      {/* Add New User */}
      {component.props.toolbar &&
        component.props.toolbar.some((menu) => menu.key === "addNewUser") && (
          <>
            <Modal
              open={isAddNewUserModalOpen}
              okText="Add"
              cancelText="Cancel"
              onOk={() => addNewUserForm.submit()}
              onCancel={() => closeAddNewUserForm()}
              okButtonProps={{
                disabled: isDisableAddUserButton,
              }}
              confirmLoading={loadingStatus["add_new_user"]}
              destroyOnClose
            >
              <AddNewUserForm
                form={addNewUserForm}
                setIsAddNewUserModalOpen={setIsAddNewUserModalOpen}
                setIsDisableAddUserButton={setIsDisableAddUserButton}
                loadingStatus={loadingStatus}
                setLoadingStatus={setLoadingStatus}
                loadingStatusKey="add_new_user"
                reloadUsersData={reloadData}
                rowData={rowData}
              />
            </Modal>
          </>
        )}
    </div>
  );
});
