# Dice Analysis Tab Options Fix

## Problem Description

When clicking the "Apply" button in the dice analysis tab options component, the grid would reload but the filter options from the form were not included in the grid's data request payload. This meant that the filters (like "Stats based on", "Show Test Numbers", etc.) were not being applied to the data.

## Root Cause

The issue was a mismatch between the keys used to store and retrieve grid filters:

1. **Storage**: The `DiceAnalysisTabOptions` component was storing filters in the `reloadGridFilters` store under keys derived from component props using the `deriveGridKeys()` function.

2. **Retrieval**: The `YHGrid` component was looking for filters under `component.reload_grid_key` when making data requests.

3. **Mismatch**: These keys didn't always match, causing filters to be stored under one key but looked up under another.

## Solution

### 1. Enhanced YHGrid Component

**File**: `src/utils/grid/yh_grid.jsx`

Added methods to the imperative handle to expose the component's configuration:

```javascript
React.useImperativeHandle(ref, () => ({
  reloadGridData: () => {
    reloadData();
  },
  isGridReady: () => {
    return isGridReady;
  },
  getComponent: () => {
    return component;
  },
  getReloadGridKey: () => {
    return component.reload_grid_key;
  },
}));
```

### 2. Fixed DiceAnalysisTabOptions Component

**File**: `src/utils/components/dice_analysis_tab_options.jsx`

Modified the `handleApply` function to:

1. Collect all possible grid keys where filters should be stored
2. Query actual grid components for their `reload_grid_key` values
3. Set filters under all relevant keys to ensure they're found by grid components

```javascript
const handleApply = (values) => {
  const updatedFilters = Helper.cloneObject(reloadGridFilters);
  const gridFiltersPayload = mapFormValuesToGrid(values);

  // Get all possible grid keys that might be used by the target grids
  const allPossibleKeys = new Set(targetGridKeys);

  // Get reload_grid_key from actual grid components
  targetGridComponentNames.forEach((componentName) => {
    const registeredGridIds = gridIds?.[pageKey]?.[componentName] ?? [];
    const activeTabKey = prerenderData?.tab_key;
    const gridIdsForActiveTab = activeTabKey
      ? registeredGridIds.filter((gridId) => gridId.includes(activeTabKey))
      : registeredGridIds;

    const idsToCheck = gridIdsForActiveTab.length
      ? gridIdsForActiveTab
      : registeredGridIds;

    idsToCheck.forEach((gridId) => {
      const gridRef = gridComponentRefs[gridId];
      if (gridRef?.current?.getReloadGridKey) {
        const reloadGridKey = gridRef.current.getReloadGridKey();
        if (reloadGridKey) {
          allPossibleKeys.add(reloadGridKey);
        }
      }
      // Also add the component name as fallback
      allPossibleKeys.add(componentName);
    });
  });

  // Update filters for all possible keys
  allPossibleKeys.forEach((key) => {
    const currentFilters = Helper.cloneObject(updatedFilters[key] ?? {});

    Object.values(formToGridFieldMap).forEach((gridKey) => {
      if (gridFiltersPayload[gridKey] !== undefined) {
        currentFilters[gridKey] = gridFiltersPayload[gridKey];
      } else {
        delete currentFilters[gridKey];
      }
    });

    updatedFilters[key] = currentFilters;
  });

  setReloadGridFilters(updatedFilters);
  reloadTargetGrids();
};
```

## How It Works

1. **Form Submission**: When user clicks "Apply", the `handleApply` function is called with form values
2. **Key Collection**: The function collects all possible keys where filters should be stored:
   - Original `targetGridKeys` from component props
   - Actual `reload_grid_key` values from registered grid components
   - Component names as fallbacks
3. **Filter Storage**: Sets the converted filter values under all collected keys in the `reloadGridFilters` store
4. **Grid Reload**: Triggers grid reload which causes grids to fetch new data
5. **Filter Application**: When grids fetch data, they find the filters under their expected `reload_grid_key` and include them in the request payload

## Testing

To verify the fix works:

1. Navigate to a page with dice analysis tab options
2. Fill in filter values (Stats based on, Show Test Numbers, etc.)
3. Click "Apply" button
4. Check browser network tab to verify filter parameters are in the grid data request
5. Confirm that grid data is filtered according to the applied options

## Benefits

- **Robust**: Works regardless of how grid keys are configured
- **Backward Compatible**: Doesn't break existing functionality
- **Future Proof**: Handles different key derivation strategies
- **Minimal Impact**: Only affects the specific components involved in the issue
