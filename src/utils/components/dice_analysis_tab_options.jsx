"use client";

import { useEffect, useMemo } from "react";
import { Button, Form, Input, Checkbox, Select, Row, Col, Card } from "antd";
import { useBoundStore } from "../../store/store";
import Helper from "../helper";
import { OptionsList } from "../forms/options_list";

const DEFAULT_TARGET_GRID = "die_level_tests_table";

const formToGridFieldMap = {
  statsBasedOn: "stats_type",
  showTestNumbers: "atnum_show_only",
  excludeTestNumbers: "atnum_exclude",
  showTestNames: "tname_show_only",
  excludeTestNames: "tname_exclude",
  filterOpenShort: "filter_open_shorts",
};

const normalizeToArray = (value) => {
  if (!value) return [];
  return Array.isArray(value) ? value : [value];
};

const mapGridFiltersToForm = (gridFilters = {}) => {
  return Object.entries(formToGridFieldMap).reduce(
    (acc, [formKey, gridKey]) => {
      if (gridFilters[gridKey] === undefined || gridFilters[gridKey] === null) {
        return acc;
      }

      if (formKey === "filterOpenShort") {
        const value = gridFilters[gridKey];
        acc[formKey] =
          value === true ||
          value === "true" ||
          value === 1 ||
          value === "1" ||
          value === "Y";
      } else {
        acc[formKey] = gridFilters[gridKey];
      }

      return acc;
    },
    {},
  );
};

const mapFormValuesToGrid = (formValues = {}) => {
  return Object.entries(formToGridFieldMap).reduce(
    (acc, [formKey, gridKey]) => {
      const rawValue = formValues[formKey];

      if (
        rawValue === undefined ||
        rawValue === null ||
        (typeof rawValue === "string" && rawValue.trim() === "")
      ) {
        return acc;
      }

      if (formKey === "filterOpenShort") {
        if (rawValue === true) {
          acc[gridKey] = true;
        }
        return acc;
      }

      acc[gridKey] = typeof rawValue === "string" ? rawValue.trim() : rawValue;

      return acc;
    },
    {},
  );
};

const deriveGridKeys = (component) => {
  const candidates = normalizeToArray(
    component?.props?.reload_grid_keys ??
      component?.props?.target_grid_keys ??
      component?.props?.target_grid_key ??
      component?.props?.grid_component_names ??
      component?.props?.grid_component_name ??
      component?.reload_grid_key ??
      component?.props?.grid_name ??
      DEFAULT_TARGET_GRID,
  );

  return candidates.filter(Boolean);
};

const deriveGridComponentNames = (component) => {
  const candidates = normalizeToArray(
    component?.props?.grid_component_names ??
      component?.props?.grid_component_name ??
      component?.props?.target_grid_name ??
      DEFAULT_TARGET_GRID,
  );

  return candidates.filter(Boolean);
};

/**
 * Dice analysis tab content options component
 *
 * @returns {JSX.Element}
 */
const DiceAnalysisTabOptions = ({ component, pageKey, prerenderData }) => {
  const [form] = Form.useForm();
  const reloadGridFilters = useBoundStore((state) => state.reloadGridFilters);
  const setReloadGridFilters = useBoundStore(
    (state) => state.setReloadGridFilters,
  );
  const setReloadGrid = useBoundStore((state) => state.setReloadGrid);
  const setReloadGrids = useBoundStore((state) => state.setReloadGrids);
  const gridComponentRefs = useBoundStore((state) => state.gridComponentRefs);
  const gridIds = useBoundStore((state) => state.gridIds);

  const targetGridKeys = useMemo(() => {
    const keys = deriveGridKeys(component);
    return keys.length > 0 ? Array.from(new Set(keys)) : [DEFAULT_TARGET_GRID];
  }, [component]);

  const statsTypeOptions = useMemo(() => {
    return OptionsList.stats_type.filter((option) => option.hidden !== true);
  }, []);

  const targetGridComponentNames = useMemo(() => {
    const names = deriveGridComponentNames(component);
    return names.length > 0
      ? Array.from(new Set(names))
      : [DEFAULT_TARGET_GRID];
  }, [component]);

  useEffect(() => {
    if (!targetGridKeys.length) {
      return;
    }

    const updatedFilters = Helper.cloneObject(reloadGridFilters);
    let shouldUpdateFilters = false;

    targetGridKeys.forEach((key) => {
      if (updatedFilters[key] === undefined) {
        updatedFilters[key] = Helper.cloneObject(
          component?.props?.default_filters ?? {},
        );
        shouldUpdateFilters = true;
      }
    });

    if (shouldUpdateFilters) {
      setReloadGridFilters(updatedFilters);
    }

    const activeFilters =
      targetGridKeys
        .map((key) => updatedFilters[key])
        .find((value) => value && Object.keys(value).length > 0) ??
      component?.props?.default_filters ??
      {};

    form.setFieldsValue(mapGridFiltersToForm(activeFilters));
  }, [
    component?.props?.default_filters,
    form,
    reloadGridFilters,
    setReloadGridFilters,
    targetGridKeys,
  ]);

  const reloadTargetGrids = () => {
    if (!targetGridKeys.length) {
      return;
    }

    setReloadGrids([...targetGridKeys]);
    setReloadGrid(Date.now());

    requestAnimationFrame(() => {
      targetGridComponentNames.forEach((componentName) => {
        const registeredGridIds = gridIds?.[pageKey]?.[componentName] ?? [];
        const activeTabKey = prerenderData?.tab_key;
        const gridIdsForActiveTab = activeTabKey
          ? registeredGridIds.filter((gridId) => gridId.includes(activeTabKey))
          : registeredGridIds;

        const idsToReload = gridIdsForActiveTab.length
          ? gridIdsForActiveTab
          : registeredGridIds;

        idsToReload.forEach((gridId) => {
          gridComponentRefs[gridId]?.current?.reloadGridData?.();
        });
      });
    });
  };

  const handleApply = (values) => {
    const updatedFilters = Helper.cloneObject(reloadGridFilters);
    const gridFiltersPayload = mapFormValuesToGrid(values);

    // Get all possible grid keys that might be used by the target grids
    const allPossibleKeys = new Set(targetGridKeys);

    // Get reload_grid_key from actual grid components
    targetGridComponentNames.forEach((componentName) => {
      const registeredGridIds = gridIds?.[pageKey]?.[componentName] ?? [];
      const activeTabKey = prerenderData?.tab_key;
      const gridIdsForActiveTab = activeTabKey
        ? registeredGridIds.filter((gridId) => gridId.includes(activeTabKey))
        : registeredGridIds;

      const idsToCheck = gridIdsForActiveTab.length
        ? gridIdsForActiveTab
        : registeredGridIds;

      idsToCheck.forEach((gridId) => {
        const gridRef = gridComponentRefs[gridId];
        if (gridRef?.current?.getReloadGridKey) {
          const reloadGridKey = gridRef.current.getReloadGridKey();
          if (reloadGridKey) {
            allPossibleKeys.add(reloadGridKey);
          }
        }
        // Also add the component name as fallback
        allPossibleKeys.add(componentName);
      });
    });

    // Update filters for all possible keys
    allPossibleKeys.forEach((key) => {
      const currentFilters = Helper.cloneObject(updatedFilters[key] ?? {});

      Object.values(formToGridFieldMap).forEach((gridKey) => {
        if (gridFiltersPayload[gridKey] !== undefined) {
          currentFilters[gridKey] = gridFiltersPayload[gridKey];
        } else {
          delete currentFilters[gridKey];
        }
      });

      updatedFilters[key] = currentFilters;
    });

    setReloadGridFilters(updatedFilters);
    reloadTargetGrids();
  };

  return (
    <Card
      className="w-full"
      size="small"
      title="Options"
      classNames={{ header: "px-3 py-2", body: "p-3" }}
    >
      <Form
        form={form}
        layout="vertical"
        className="w-full"
        onFinish={handleApply}
      >
        <Row gutter={[16, 16]} wrap align="middle">
          <Col flex="1 0 260px">
            <Form.Item label="Stats based on" name="statsBasedOn">
              <Select placeholder="All" allowClear options={statsTypeOptions} />
            </Form.Item>
          </Col>

          <Col flex="1 0 260px">
            <Form.Item label="Show Test Numbers" name="showTestNumbers">
              <Input placeholder="e.g. 10–20" allowClear />
            </Form.Item>
          </Col>

          <Col flex="1 0 260px">
            <Form.Item label="Exclude Test Numbers" name="excludeTestNumbers">
              <Input placeholder="e.g. 10–20" allowClear />
            </Form.Item>
          </Col>

          <Col flex="1 0 260px">
            <Form.Item label="Show Test Names" name="showTestNames">
              <Input placeholder="e.g. IDD,VDD" allowClear />
            </Form.Item>
          </Col>

          <Col flex="1 0 260px">
            <Form.Item label="Exclude Test Names" name="excludeTestNames">
              <Input placeholder="e.g. IDD,VDD" allowClear />
            </Form.Item>
          </Col>

          <Col flex="0 0 auto">
            <Form.Item name="filterOpenShort" valuePropName="checked">
              <Checkbox>Filter Open/Short Tests</Checkbox>
            </Form.Item>
          </Col>

          <Col flex="0 0 auto" className="ml-auto flex items-center h-full">
            <Button type="primary" onClick={() => form.submit()}>
              Apply
            </Button>
          </Col>
        </Row>
      </Form>
    </Card>
  );
};

export default DiceAnalysisTabOptions;
