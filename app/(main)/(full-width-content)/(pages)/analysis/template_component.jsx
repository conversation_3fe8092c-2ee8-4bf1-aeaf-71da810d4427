import { useEffect, useRef, useState } from "react";
import dynamic from "next/dynamic";
import { useFullScreenHandle } from "react-full-screen";
import { useInView } from "react-intersection-observer";
import { merge } from "lodash";
import { useBoundStore } from "../../../../../src/store/store";
const BarHistogram = dynamic(
  () => import("../../../../../src/utils/charts/bar_histogram"),
  {
    ssr: false,
  },
);
import ChartWrapper from "../../../../../src/utils/charts/chart_wrapper";
const CurveHistogram = dynamic(
  () => import("../../../../../src/utils/charts/curve_histogram"),
  {
    ssr: false,
  },
);
const GroupedColumnWithLine = dynamic(
  () => import("../../../../../src/utils/charts/grouped_column_with_line"),
  {
    ssr: false,
  },
);
const Pareto = dynamic(() => import("../../../../../src/utils/charts/pareto"), {
  ssr: false,
});
const ParetoWithLine = dynamic(
  () => import("../../../../../src/utils/charts/pareto_with_line"),
  {
    ssr: false,
  },
);
const StackedColumn = dynamic(
  () => import("../../../../../src/utils/charts/stacked_column"),
  {
    ssr: false,
  },
);
const WaferMap = dynamic(
  () => import("../../../../../src/utils/charts/wafer_map"),
  {
    ssr: false,
  },
);
import BinPatterns from "../../../../../src/utils/charts/bin_patterns";
const ZonalChart = dynamic(
  () => import("../../../../../src/utils/charts/zonal_chart"),
  {
    ssr: false,
  },
);
import StatisticCard from "../../../../../src/utils/components/statistic_card";
import YHGrid from "../../../../../src/utils/grid/yh_grid";
import Helper from "../../../../../src/utils/helper";
const NormalProbability = dynamic(
  () => import("../../../../../src/utils/charts/normal_probability"),
  {
    ssr: false,
  },
);
const Scatter = dynamic(
  () => import("../../../../../src/utils/charts/scatter"),
  {
    ssr: false,
  },
);
const ScatterWithLine = dynamic(
  () => import("../../../../../src/utils/charts/scatter_with_line"),
  {
    ssr: false,
  },
);
import Chartspace from "../../../../../src/utils/components/chartspace";
const BoxPlot = dynamic(
  () => import("../../../../../src/utils/charts/box_plot"),
  {
    ssr: false,
  },
);
const BoxPlotWithLine = dynamic(
  () => import("../../../../../src/utils/charts/box_plot_with_line"),
  {
    ssr: false,
  },
);
import OEEStatisticCard from "../../../../../src/utils/components/oee_statistic_card";
const Line = dynamic(() => import("../../../../../src/utils/charts/line"), {
  ssr: false,
});
import WaferMapGallery from "../../../../../src/utils/components/wafer_map_gallery";
import LotStatsCardFinalYield from "../../../../../src/utils/components/lot_stats_card_final_yield";
import LotStatsCardDatalogSummary from "../../../../../src/utils/components/lot_stats_card_datalog_summary";
import LotStatsCardTopFailingBins from "../../../../../src/utils/components/lot_stats_card_top_failing_bins";
import LotStatsCardTopFailingTests from "../../../../../src/utils/components/lot_stats_card_top_failing_tests";
import ReprobeAnalysis from "../../../../../src/utils/components/reprobe_analysis";
const StackedColumnWithLine = dynamic(
  () => import("../../../../../src/utils/charts/stacked_column_with_line"),
  {
    ssr: false,
  },
);
const Bar = dynamic(() => import("../../../../../src/utils/charts/bar"), {
  ssr: false,
});
import ChartHelper from "../../../../../src/utils/charts/chart_helper";
import GridHelper from "../../../../../src/utils/grid/grid_helper";
import FiltersDisplay from "../../../../../src/utils/components/filters_display";
import MergedScatterCharts from "../../../../../src/utils/components/merged_scatter_charts";
import PerDatalogScatterCharts from "../../../../../src/utils/components/per_datalog_scatter_charts";
import PerGroupWaferCharts from "../../../../../src/utils/charts/components/per_group_wafer_charts";
import PerDieWaferCharts from "../../../../../src/utils/charts/components/per_die_wafer_charts";
import { ComponentNameMapper } from "../../../../../src/utils/grid/component_name_mapper";
import NpiSimulation from "../npi/npi_simulation";
import GenerateReportOptions from "../npi/generate_report_options";
import RecipeReportHeader from "../npi/recipe_report_header";
import AnalysisSetTabOptions from "../npi/analysis_set_tab_options";
import DiceAnalysisTabOptions from "../../../../../src/utils/components/dice_analysis_tab_options";
import GageChartsGallery from "../npi/gage_charts_gallery";

/**
 * Page template component
 *
 * @param {object} component
 * @param {object} cellActions
 * @param {string} pageKey
 * @param {function} testStatsInfoHandler Handler of the setting the test stats info
 * @param {object} filters
 * @param {function} setFilters
 * @param {boolean} isChartBoosted
 * @param {function} setIsChartBoosted
 * @param {object} prerenderData
 * @param {object} requiredData
 * @param {boolean} showBoostWarning
 * @param {function} setHighchartsChart
 * @returns {JSX.Element}
 */
export const TemplateComponent = ({
  component,
  cellActions,
  pageKey,
  testStatsInfoHandler,
  filters,
  setFilters,
  isChartBoosted,
  setIsChartBoosted,
  prerenderData = {},
  requiredData,
  showBoostWarning = true,
  setHighchartsChart,
}) => {
  const [templateComponent, setTemplateComponent] = useState();
  const [shouldRenderComponent, setShouldRenderComponent] = useState(false);
  const [waferIdOptions, setWaferIdOptions] = useState([]);
  const [hasChartData, setHasChartData] = useState(true);
  const [chartCustomData] = useState({});
  const urlParams = useBoundStore((state) => state.urlParams);
  const chartKeys = useBoundStore((state) => state.chartKeys);
  const gridIds = useBoundStore((state) => state.gridIds);
  const pageMeta = useBoundStore((state) => state.pageMeta);
  const componentWrapperRef = useRef();
  const componentRef = useRef();
  const fullScreenHandle = useFullScreenHandle();
  const { ref, inView } = useInView({
    triggerOnce: true,
    rootMargin: "200px 0px",
  });
  const chartKey = Helper.generateLabelByData(
    `${pageKey}_${component.component}_${component.id}_${component.name}`,
    { ...filters[pageKey], ...prerenderData },
  );
  const gridId = `${pageKey}_${component.id}_${component.name}`;

  // initialize chart custom data
  ChartHelper.initChartCustomData(chartCustomData, chartKey);

  useEffect(() => {
    const filtersCopy = Helper.cloneObject(filters);
    filtersCopy[pageKey] = merge(filtersCopy[pageKey], urlParams[pageKey]);
    filtersCopy[pageKey].analysis_type = pageMeta.analysis_type;
    if (typeof setFilters === "function") {
      setFilters(filtersCopy);
    }
    // assign page filters so that it will be updated immediately without rerendering component
    filters[pageKey] = filtersCopy[pageKey];
    setShouldRenderComponent(true);

    if (component.type === "chart") {
      // store chart key
      ChartHelper.storeChartKey(chartKey, chartKeys, pageKey, component.name);
    }
    if (component.type === "table") {
      // store grid key
      GridHelper.storeGridId(gridId, gridIds, pageKey, component.name);
    }
  }, []);

  useEffect(() => {
    if (shouldRenderComponent) {
      setShouldRenderComponent(!shouldRenderComponent);
      renderComponent(component);
    }
  }, [shouldRenderComponent]);

  /**
   * Set template component to be rendered on page
   *
   * @param {object} component
   */
  const renderComponent = (component) => {
    let renderedComponent;

    switch (component.type) {
      case "table":
        renderedComponent = (
          <YHGrid
            ref={componentWrapperRef}
            gridRef={componentRef}
            gridId={gridId}
            component={component}
            filters={filters}
            cellActions={cellActions}
            pageKey={pageKey}
            gridOptions={{
              rowModelType: component.props.settings.is_server_side
                ? "serverSide"
                : "clientSide",
            }}
            prerenderData={prerenderData}
          />
        );
        break;
      case "chart":
        switch (component.component) {
          case "pareto":
            renderedComponent = (
              <Pareto
                chartRef={componentRef}
                component={component}
                filters={filters}
                pageKey={pageKey}
                chartKey={chartKey}
                chartCustomData={chartCustomData}
                fullScreenHandle={fullScreenHandle}
                prerenderData={prerenderData}
              />
            );
            break;
          case "pareto_with_line":
            renderedComponent = (
              <ParetoWithLine
                ref={componentWrapperRef}
                chartRef={componentRef}
                component={component}
                filters={filters}
                pageKey={pageKey}
                chartKey={chartKey}
                chartCustomData={chartCustomData}
                setIsChartBoosted={setIsChartBoosted}
                setHasChartData={setHasChartData}
                fullScreenHandle={fullScreenHandle}
                prerenderData={prerenderData}
              />
            );
            break;
          case "stacked_column":
            renderedComponent = (
              <StackedColumn
                ref={componentWrapperRef}
                chartRef={componentRef}
                component={component}
                filters={filters}
                pageKey={pageKey}
                chartKey={chartKey}
                chartCustomData={chartCustomData}
                setIsChartBoosted={setIsChartBoosted}
                setHasChartData={setHasChartData}
                fullScreenHandle={fullScreenHandle}
                prerenderData={prerenderData}
              />
            );
            break;
          case "stacked_column_with_line":
            renderedComponent = (
              <StackedColumnWithLine
                ref={componentWrapperRef}
                chartRef={componentRef}
                component={component}
                filters={filters}
                pageKey={pageKey}
                chartKey={chartKey}
                chartCustomData={chartCustomData}
                setHasChartData={setHasChartData}
                fullScreenHandle={fullScreenHandle}
                prerenderData={prerenderData}
              />
            );
            break;
          case "columns_with_lines":
            renderedComponent = (
              <GroupedColumnWithLine
                chartRef={componentRef}
                component={component}
                filters={filters}
                pageKey={pageKey}
                chartKey={chartKey}
                chartCustomData={chartCustomData}
                setHasChartData={setHasChartData}
                fullScreenHandle={fullScreenHandle}
                prerenderData={prerenderData}
              />
            );
            break;
          case "yield_wafer_map":
          case "composite_wafer_map":
          case "parametric_wafer_map":
            renderedComponent = (
              <WaferMap
                ref={componentWrapperRef}
                chartRef={componentRef}
                component={component}
                filters={filters}
                pageKey={pageKey}
                waferIdOptions={waferIdOptions}
                setWaferIdOptions={setWaferIdOptions}
                chartCustomData={chartCustomData}
                chartKey={chartKey}
                isChartBoosted={isChartBoosted}
                setIsChartBoosted={setIsChartBoosted}
                setHasChartData={setHasChartData}
                fullScreenHandle={fullScreenHandle}
                prerenderData={prerenderData}
                requiredData={requiredData}
              />
            );
            break;
          case "zonal_chart":
            renderedComponent = (
              <ZonalChart
                ref={componentWrapperRef}
                chartRef={componentRef}
                component={component}
                filters={filters}
                pageKey={pageKey}
                chartKey={chartKey}
                chartCustomData={chartCustomData}
                setHasChartData={setHasChartData}
                fullScreenHandle={fullScreenHandle}
                prerenderData={prerenderData}
              />
            );
            break;
          case "boxplot":
            renderedComponent = (
              <BoxPlot
                ref={componentWrapperRef}
                chartRef={componentRef}
                component={component}
                filters={filters}
                pageKey={pageKey}
                chartKey={chartKey}
                chartCustomData={chartCustomData}
                setHasChartData={setHasChartData}
                setHighchartsChart={setHighchartsChart}
                fullScreenHandle={fullScreenHandle}
                prerenderData={prerenderData}
              />
            );
            break;
          case "boxplot_with_line":
            renderedComponent = (
              <BoxPlotWithLine
                ref={componentWrapperRef}
                chartRef={componentRef}
                component={component}
                filters={filters}
                pageKey={pageKey}
                chartKey={chartKey}
                chartCustomData={chartCustomData}
                setHasChartData={setHasChartData}
                fullScreenHandle={fullScreenHandle}
                prerenderData={prerenderData}
              />
            );
            break;
          case "curve_histogram":
            renderedComponent = (
              <CurveHistogram
                ref={componentWrapperRef}
                chartRef={componentRef}
                component={component}
                filters={filters}
                pageKey={pageKey}
                chartKey={chartKey}
                chartCustomData={chartCustomData}
                setHasChartData={setHasChartData}
                setHighchartsChart={setHighchartsChart}
                fullScreenHandle={fullScreenHandle}
                prerenderData={prerenderData}
              />
            );
            break;
          case "bar_histogram":
            renderedComponent = (
              <BarHistogram
                ref={componentWrapperRef}
                chartRef={componentRef}
                component={component}
                filters={filters}
                pageKey={pageKey}
                chartKey={chartKey}
                chartCustomData={chartCustomData}
                setHasChartData={setHasChartData}
                setHighchartsChart={setHighchartsChart}
                fullScreenHandle={fullScreenHandle}
                prerenderData={prerenderData}
              />
            );
            break;
          case "qq_plot":
            renderedComponent = (
              <NormalProbability
                chartRef={componentRef}
                component={component}
                filters={filters}
                pageKey={pageKey}
                chartKey={chartKey}
                chartCustomData={chartCustomData}
                setIsChartBoosted={setIsChartBoosted}
                setHasChartData={setHasChartData}
                setHighchartsChart={setHighchartsChart}
                fullScreenHandle={fullScreenHandle}
                prerenderData={prerenderData}
              />
            );
            break;
          case "scatter":
            renderedComponent = (
              <Scatter
                ref={componentWrapperRef}
                chartRef={componentRef}
                component={component}
                filters={filters}
                pageKey={pageKey}
                chartKey={chartKey}
                chartCustomData={chartCustomData}
                testStatsInfoHandler={testStatsInfoHandler}
                setIsChartBoosted={setIsChartBoosted}
                setHasChartData={setHasChartData}
                fullScreenHandle={fullScreenHandle}
                prerenderData={prerenderData}
              />
            );
            break;
          case "scatter_with_line":
            renderedComponent = (
              <ScatterWithLine
                ref={componentWrapperRef}
                chartRef={componentRef}
                component={component}
                filters={filters}
                pageKey={pageKey}
                chartKey={chartKey}
                chartCustomData={chartCustomData}
                testStatsInfoHandler={testStatsInfoHandler}
                isChartBoosted={isChartBoosted}
                setIsChartBoosted={setIsChartBoosted}
                setHasChartData={setHasChartData}
                fullScreenHandle={fullScreenHandle}
                prerenderData={prerenderData}
              />
            );
            break;
          case "line":
            renderedComponent = (
              <Line
                ref={componentWrapperRef}
                chartRef={componentRef}
                component={component}
                filters={filters}
                pageKey={pageKey}
                chartKey={chartKey}
                chartCustomData={chartCustomData}
                setIsChartBoosted={setIsChartBoosted}
                setHasChartData={setHasChartData}
                setHighchartsChart={setHighchartsChart}
                fullScreenHandle={fullScreenHandle}
                prerenderData={prerenderData}
              />
            );
            break;
          case "bar":
            renderedComponent = (
              <Bar
                ref={componentWrapperRef}
                chartRef={componentRef}
                component={component}
                filters={filters}
                pageKey={pageKey}
                chartKey={chartKey}
                chartCustomData={chartCustomData}
                setHasChartData={setHasChartData}
                fullScreenHandle={fullScreenHandle}
                prerenderData={prerenderData}
              />
            );
            break;
        }
        break;
      case "data_display":
        switch (component.component) {
          case "oee_statistic_card":
            renderedComponent = (
              <OEEStatisticCard
                component={component}
                filters={filters}
                pageKey={pageKey}
                prerenderData={prerenderData}
              />
            );
            break;
          case "lot_stats_card_final_yield":
            renderedComponent = (
              <LotStatsCardFinalYield
                component={component}
                filters={filters}
                pageKey={pageKey}
                prerenderData={prerenderData}
              />
            );
            break;
          case "lot_stats_card_datalog_summary":
            renderedComponent = (
              <LotStatsCardDatalogSummary
                component={component}
                filters={filters}
                pageKey={pageKey}
                prerenderData={prerenderData}
              />
            );
            break;
          case "lot_stats_card_top_failing_bins":
            renderedComponent = (
              <LotStatsCardTopFailingBins
                component={component}
                filters={filters}
                pageKey={pageKey}
                prerenderData={prerenderData}
              />
            );
            break;
          case "lot_stats_card_top_failing_tests":
            renderedComponent = (
              <LotStatsCardTopFailingTests
                component={component}
                filters={filters}
                pageKey={pageKey}
                prerenderData={prerenderData}
              />
            );
            break;
          case "reprobe_analysis":
            renderedComponent = (
              <ReprobeAnalysis
                component={component}
                filters={filters}
                pageKey={pageKey}
                prerenderData={prerenderData}
              />
            );
            break;
          case "npi_generate_report_options":
            renderedComponent = (
              <GenerateReportOptions
                component={component}
                filters={filters}
                pageKey={pageKey}
              />
            );
            break;
          case "recipe_report_header":
            renderedComponent = (
              <RecipeReportHeader
                component={component}
                filters={filters}
                pageKey={pageKey}
              />
            );
            break;
          case "analysis_set_tab_options":
            renderedComponent = (
              <AnalysisSetTabOptions
                component={component}
                filters={filters}
                pageKey={pageKey}
                prerenderData={prerenderData}
              />
            );
            break;
          case "dice_analysis_tab_options":
            renderedComponent = (
              <DiceAnalysisTabOptions
                component={component}
                filters={filters}
                pageKey={pageKey}
                prerenderData={prerenderData}
              />
            );
            break;
          default:
            renderedComponent = (
              <StatisticCard
                component={component}
                filters={filters}
                pageKey={pageKey}
                prerenderData={prerenderData}
              />
            );
        }
        break;
      case "charts_display":
        switch (component.component) {
          case "chartspace":
            renderedComponent = (
              <Chartspace
                component={component}
                filters={filters}
                pageKey={pageKey}
                chartCustomData={chartCustomData}
                testStatsInfoHandler={testStatsInfoHandler}
                prerenderData={prerenderData}
              />
            );
            break;
          case "wafer_map_gallery":
            renderedComponent = (
              <WaferMapGallery
                filters={filters}
                pageKey={pageKey}
                chartCustomData={chartCustomData}
                testStatsInfoHandler={testStatsInfoHandler}
                prerenderData={prerenderData}
              />
            );
            break;
          case "npi_simulation":
            renderedComponent = (
              <NpiSimulation
                component={component}
                filters={filters}
                pageKey={pageKey}
              />
            );
            break;
          case "composite_wafer_map":
            renderedComponent = (
              <BinPatterns
                chartRef={componentRef}
                component={component}
                filters={filters}
                pageKey={pageKey}
                waferIdOptions={waferIdOptions}
                setWaferIdOptions={setWaferIdOptions}
                chartCustomData={chartCustomData}
                chartKey={chartKey}
                isChartBoosted={isChartBoosted}
                setIsChartBoosted={setIsChartBoosted}
                setHasChartData={setHasChartData}
                fullScreenHandle={fullScreenHandle}
                prerenderData={prerenderData}
              />
            );
            break;
          case "merged_scatter_charts":
            renderedComponent = (
              <MergedScatterCharts
                component={component}
                componentName={ComponentNameMapper.lot_parametric_scatter}
                filters={filters}
                pageKey={pageKey}
                chartCustomData={chartCustomData}
                testStatsInfoHandler={testStatsInfoHandler}
                prerenderData={prerenderData}
              />
            );
            break;
          case "mpr_merged_scatter_charts":
            renderedComponent = (
              <MergedScatterCharts
                component={component}
                componentName={ComponentNameMapper.parametric_mpr_scatter}
                filters={filters}
                pageKey={pageKey}
                chartCustomData={chartCustomData}
                testStatsInfoHandler={testStatsInfoHandler}
                prerenderData={prerenderData}
              />
            );
            break;
          case "per_datalog_scatter_charts":
            renderedComponent = (
              <PerDatalogScatterCharts
                component={component}
                filters={filters}
                pageKey={pageKey}
                chartCustomData={chartCustomData}
                testStatsInfoHandler={testStatsInfoHandler}
                prerenderData={prerenderData}
              />
            );
            break;
          case "per_group_wafer_charts":
            renderedComponent = (
              <PerGroupWaferCharts
                component={component}
                filters={filters}
                pageKey={pageKey}
                chartCustomData={chartCustomData}
                testStatsInfoHandler={testStatsInfoHandler}
                prerenderData={prerenderData}
              />
            );
            break;
          case "per_die_wafer_charts":
            renderedComponent = (
              <PerDieWaferCharts
                component={component}
                filters={filters}
                pageKey={pageKey}
                chartCustomData={chartCustomData}
                testStatsInfoHandler={testStatsInfoHandler}
                prerenderData={prerenderData}
              />
            );
            break;
          case "gage_charts_gallery":
            renderedComponent = (
              <GageChartsGallery
                filters={filters}
                setFilters={setFilters}
                pageKey={pageKey}
                chartCustomData={chartCustomData}
                testStatsInfoHandler={testStatsInfoHandler}
              />
            );
            break;
        }
        break;
      case "custom":
        switch (component.component) {
          case "filters_display":
            renderedComponent = (
              <FiltersDisplay
                component={component}
                filters={filters}
                pageKey={pageKey}
                prerenderData={prerenderData}
              />
            );
            break;
        }
        break;
    }
    setTemplateComponent(renderedComponent);
  };

  return (
    <div ref={ref} className="h-full">
      {inView &&
        (component.type === "text" ? (
          <span>
            {Helper.getTextElement(component.value, component.text_type)}
          </span>
        ) : (
          <div className="w-full h-full">
            {component.type === "chart" ? (
              <ChartWrapper
                key={chartKey}
                chartComponent={templateComponent}
                chartRef={componentRef}
                component={component}
                pageKey={pageKey}
                waferIdOptions={waferIdOptions}
                chartCustomData={chartCustomData}
                chartKey={chartKey}
                hasChartData={hasChartData}
                isChartBoosted={isChartBoosted}
                fullScreenHandle={fullScreenHandle}
                prerenderData={prerenderData}
                showBoostWarning={showBoostWarning}
              />
            ) : (
              templateComponent
            )}
          </div>
        ))}
    </div>
  );
};
